/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Arial', 'Microsoft YaHei', sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

.app {
  position: relative;
  width: 100vw;
  height: 100vh;
}

/* XR按钮样式 */
.xr-buttons {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  gap: 10px;
}

.xr-buttons button {
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 25px;
  font-size: 14px;
  font-weight: bold;
  color: #333;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.xr-buttons button:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.xr-buttons button:active {
  transform: translateY(0);
}

/* 加载动画 */
.loading {
  text-align: center;
  color: white;
  padding: 20px;
}

.loading h2 {
  margin-bottom: 20px;
  font-size: 24px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* POI提示框样式 */
.poi-tooltip {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px 15px;
  border-radius: 8px;
  max-width: 200px;
  text-align: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.poi-tooltip h4 {
  margin: 0 0 5px 0;
  font-size: 14px;
  color: #4ecdc4;
}

.poi-tooltip p {
  margin: 0;
  font-size: 12px;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .xr-buttons {
    top: 10px;
    right: 10px;
    flex-direction: column;
  }

  .xr-buttons button {
    padding: 10px 16px;
    font-size: 12px;
  }

  .loading h2 {
    font-size: 20px;
  }

  .poi-tooltip {
    max-width: 150px;
    padding: 8px 12px;
  }

  .poi-tooltip h4 {
    font-size: 12px;
  }

  .poi-tooltip p {
    font-size: 10px;
  }
}

/* 脉冲动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(0.8);
  }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  .loading-spinner {
    animation: none;
  }

  * {
    transition: none !important;
    animation: none !important;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .xr-buttons button {
    background: #ffffff;
    color: #000000;
    border: 2px solid #000000;
  }

  .poi-tooltip {
    background: #000000;
    color: #ffffff;
    border: 2px solid #ffffff;
  }
}
