import React, { Suspense, useState } from 'react'
import { Canvas } from '@react-three/fiber'
import { OrbitControls, Text } from '@react-three/drei'
import { XR, VRButton, ARButton } from '@react-three/xr'
import './App.css'

// POI数据
const POI_DATA = [
  {
    id: 'main-pit',
    name: '主矿坑',
    position: [0, 0, 0],
    color: '#ff6b6b',
    description: '汤山矿坑公园的核心景观，深达数十米的废弃采石坑，现已改造为生态景观湖。',
    details: '深度约40米，面积约15公顷，开采时间1950-2010年'
  },
  {
    id: 'observation-deck',
    name: '观景台',
    position: [8, 2, -5],
    color: '#4ecdc4',
    description: '最佳观赏矿坑全景的位置，可以俯瞰整个矿坑湖和周围的生态恢复区域。',
    details: '高度海拔120米，360度全景视野，最佳观赏时间日出日落'
  },
  {
    id: 'eco-trail',
    name: '生态步道',
    position: [-6, 0, 3],
    color: '#45b7d1',
    description: '环绕矿坑的生态步道，展示了从工业废地到生态公园的转变过程。',
    details: '总长度2.5公里，步行时间约45分钟，8个观景点'
  }
]

// POI标记组件
function POIMarker({ poi, onClick, isSelected }) {
  return (
    <mesh
      position={poi.position}
      onClick={() => onClick(poi)}
      onPointerOver={(e) => {
        e.object.material.emissive.setHex(0x444444)
        document.body.style.cursor = 'pointer'
      }}
      onPointerOut={(e) => {
        e.object.material.emissive.setHex(isSelected ? 0x222222 : 0x000000)
        document.body.style.cursor = 'default'
      }}
    >
      <sphereGeometry args={[isSelected ? 0.4 : 0.3, 16, 16]} />
      <meshStandardMaterial
        color={poi.color}
        emissive={isSelected ? poi.color : '#000000'}
        emissiveIntensity={isSelected ? 0.3 : 0}
      />
    </mesh>
  )
}

// 信息面板组件
function InfoPanel({ poi, onClose }) {
  if (!poi) return null

  return (
    <div style={{
      position: 'fixed',
      top: '20px',
      right: '20px',
      width: '300px',
      background: 'rgba(255, 255, 255, 0.95)',
      backdropFilter: 'blur(10px)',
      borderRadius: '15px',
      padding: '20px',
      boxShadow: '0 10px 30px rgba(0,0,0,0.3)',
      zIndex: 1000,
      color: '#333'
    }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
        <h3 style={{ margin: 0, color: poi.color, fontSize: '1.2rem' }}>{poi.name}</h3>
        <button
          onClick={onClose}
          style={{
            background: 'none',
            border: 'none',
            fontSize: '20px',
            cursor: 'pointer',
            color: '#666'
          }}
        >
          ✕
        </button>
      </div>
      <p style={{ margin: '0 0 10px 0', lineHeight: '1.5', fontSize: '14px' }}>
        {poi.description}
      </p>
      <div style={{
        background: 'rgba(78, 205, 196, 0.1)',
        padding: '10px',
        borderRadius: '8px',
        fontSize: '13px',
        color: '#555'
      }}>
        <strong>详细信息：</strong><br />
        {poi.details}
      </div>
    </div>
  )
}

// 汤山矿坑3D场景
function BasicScene({ selectedPOI, onPOIClick, arSimulationMode }) {
  return (
    <>
      {/* 基础光照 */}
      <ambientLight intensity={0.5} />
      <directionalLight position={[10, 10, 5]} intensity={1} />

      {/* 地面 */}
      <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -2, 0]}>
        <planeGeometry args={[20, 20]} />
        <meshStandardMaterial color="#8B4513" />
      </mesh>

      {/* 矿坑水面 */}
      <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -1.8, 0]}>
        <circleGeometry args={[6, 32]} />
        <meshStandardMaterial color="#006994" transparent opacity={0.8} />
      </mesh>

      {/* POI标记 */}
      {POI_DATA.map((poi) => (
        <POIMarker
          key={poi.id}
          poi={poi}
          onClick={onPOIClick}
          isSelected={selectedPOI?.id === poi.id}
        />
      ))}

      {/* 场景标题 */}
      <Text
        position={[0, 8, -10]}
        fontSize={1}
        color="#ffffff"
        anchorX="center"
        anchorY="middle"
        outlineWidth={0.05}
        outlineColor="#000000"
      >
        汤山矿坑公园 AR 体验
      </Text>

      {/* 场景描述 */}
      <Text
        position={[0, 6.5, -10]}
        fontSize={0.4}
        color="#cccccc"
        anchorX="center"
        anchorY="middle"
        outlineWidth={0.02}
        outlineColor="#000000"
        maxWidth={15}
      >
        探索这个从废弃采石场转变为生态公园的奇迹
      </Text>

      {/* 相机控制 */}
      <OrbitControls
        enablePan={true}
        enableZoom={true}
        enableRotate={true}
        maxPolarAngle={Math.PI / 2}
      />
    </>
  )
}

// WebGL检测函数
function checkWebGL() {
  try {
    const canvas = document.createElement('canvas')
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
    return !!gl
  } catch (e) {
    return false
  }
}

// 浏览器和设备检测
function getBrowserInfo() {
  const ua = navigator.userAgent
  const isSafari = /Safari/.test(ua) && !/Chrome/.test(ua)
  const isChrome = /Chrome/.test(ua)
  const isIOS = /iPad|iPhone|iPod/.test(ua)
  const isAndroid = /Android/.test(ua)

  // 获取版本信息
  const chromeVersion = isChrome ? ua.match(/Chrome\/(\d+)/)?.[1] : null
  const iosVersion = isIOS ? ua.match(/OS (\d+)_(\d+)/)?.[1] : null
  const androidVersion = isAndroid ? ua.match(/Android (\d+)/)?.[1] : null

  return {
    isSafari,
    isChrome,
    isIOS,
    isAndroid,
    chromeVersion: chromeVersion ? parseInt(chromeVersion) : null,
    iosVersion: iosVersion ? parseInt(iosVersion) : null,
    androidVersion: androidVersion ? parseInt(androidVersion) : null
  }
}

// 设备AR支持检测
function checkDeviceARSupport(browser) {
  const issues = []

  if (browser.isIOS) {
    if (!browser.iosVersion || browser.iosVersion < 14) {
      issues.push('需要iOS 14.3+')
    }
    if (!browser.isChrome) {
      issues.push('iOS需要使用Chrome浏览器')
    }
    if (browser.isChrome && (!browser.chromeVersion || browser.chromeVersion < 94)) {
      issues.push('需要Chrome 94+')
    }
  }

  if (browser.isAndroid) {
    if (!browser.androidVersion || browser.androidVersion < 7) {
      issues.push('需要Android 7.0+')
    }
    if (!browser.isChrome) {
      issues.push('Android需要使用Chrome浏览器')
    }
    if (browser.isChrome && (!browser.chromeVersion || browser.chromeVersion < 79)) {
      issues.push('需要Chrome 79+')
    }
  }

  if (!browser.isIOS && !browser.isAndroid) {
    issues.push('桌面浏览器不支持移动AR')
  }

  return issues
}

// WebXR支持检测
async function checkWebXRSupport() {
  const browser = getBrowserInfo()
  const deviceIssues = checkDeviceARSupport(browser)

  if (!navigator.xr) {
    let reason = 'WebXR API不可用'
    if (browser.isSafari) {
      reason = 'Safari不支持WebXR'
    } else if (deviceIssues.length > 0) {
      reason = `设备不满足要求: ${deviceIssues.join(', ')}`
    }
    return { ar: false, vr: false, reason, browser, deviceIssues }
  }

  try {
    const arSupported = await navigator.xr.isSessionSupported('immersive-ar')
    const vrSupported = await navigator.xr.isSessionSupported('immersive-vr')

    let reason = '支持'
    if (!arSupported && !vrSupported) {
      if (deviceIssues.length > 0) {
        reason = `设备限制: ${deviceIssues.join(', ')}`
      } else {
        reason = '设备不支持XR或缺少ARCore/ARKit'
      }
    }

    return {
      ar: arSupported,
      vr: vrSupported,
      reason,
      browser,
      deviceIssues
    }
  } catch (e) {
    return {
      ar: false,
      vr: false,
      reason: `检测失败: ${e.message}`,
      browser,
      deviceIssues
    }
  }
}

function App() {
  const [showTest, setShowTest] = useState(true)
  const [selectedPOI, setSelectedPOI] = useState(null)
  const [webglSupported, setWebglSupported] = useState(null)
  const [xrSupport, setXrSupport] = useState(null)
  const [arSimulationMode, setArSimulationMode] = useState(false)

  React.useEffect(() => {
    setWebglSupported(checkWebGL())
    checkWebXRSupport().then(setXrSupport)
  }, [])

  const handlePOIClick = (poi) => {
    setSelectedPOI(selectedPOI?.id === poi.id ? null : poi)
  }
  return (
    <div className="app">
      {/* 切换按钮 */}
      <button
        onClick={() => setShowTest(!showTest)}
        style={{
          position: 'fixed',
          top: '20px',
          left: '20px',
          zIndex: 1000,
          padding: '10px 20px',
          background: 'rgba(78, 205, 196, 0.9)',
          color: 'white',
          border: 'none',
          borderRadius: '25px',
          cursor: 'pointer',
          fontSize: '14px',
          fontWeight: 'bold'
        }}
      >
        {showTest ? '🎮 启用3D场景' : '📋 返回测试页面'}
      </button>

      {showTest ? (
        // 测试页面
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          color: 'white',
          fontSize: '2rem',
          textAlign: 'center',
          background: 'rgba(0,0,0,0.7)',
          padding: '20px',
          borderRadius: '10px',
          zIndex: 1000
        }}>
          🏔️ 汤山矿坑公园 AR 互动平台 🏔️
          <br />
          <span style={{ fontSize: '1rem', marginTop: '10px', display: 'block' }}>
            ✅ 测试版本 - 基础功能正常
          </span>
          <div style={{
            fontSize: '0.8rem',
            marginTop: '15px',
            color: '#4ecdc4',
            display: 'flex',
            flexDirection: 'column',
            gap: '5px'
          }}>
            <div>🎮 React 应用正常运行</div>
            <div>🌐 开发服务器连接正常</div>
            <div>📱 移动端兼容测试通过</div>
            <div style={{ color: webglSupported ? '#4ecdc4' : '#ff6b6b' }}>
              {webglSupported === null ? '🔍 检测WebGL支持...' :
               webglSupported ? '✅ WebGL 支持正常' : '❌ WebGL 不支持'}
            </div>
            <div style={{ color: xrSupport?.ar || xrSupport?.vr ? '#4ecdc4' : '#feca57' }}>
              {xrSupport === null ? '🔍 检测WebXR支持...' :
               xrSupport.ar || xrSupport.vr ?
               `✅ WebXR 支持 (AR:${xrSupport.ar?'✓':'✗'} VR:${xrSupport.vr?'✓':'✗'})` :
               `⚠️ WebXR 不支持`}
            </div>
            {xrSupport?.browser && (
              <div style={{ fontSize: '0.7rem', marginTop: '5px', color: '#999' }}>
                {xrSupport.browser.isIOS && `📱 iOS ${xrSupport.browser.iosVersion || '?'}`}
                {xrSupport.browser.isAndroid && `🤖 Android ${xrSupport.browser.androidVersion || '?'}`}
                {!xrSupport.browser.isIOS && !xrSupport.browser.isAndroid && '💻 桌面'}
                {' • '}
                {xrSupport.browser.isChrome && `Chrome ${xrSupport.browser.chromeVersion || '?'}`}
                {xrSupport.browser.isSafari && 'Safari'}
                {!xrSupport.browser.isChrome && !xrSupport.browser.isSafari && '其他浏览器'}
              </div>
            )}
            <div style={{ marginTop: '10px', color: '#feca57' }}>
              点击左上角按钮体验3D场景 →
            </div>
          </div>
        </div>
      ) : (
        // 3D场景
        <>
          {/* WebGL检测 */}
          {webglSupported === false ? (
            <div style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              color: 'white',
              fontSize: '1.5rem',
              textAlign: 'center',
              background: 'rgba(255, 0, 0, 0.8)',
              padding: '20px',
              borderRadius: '10px',
              zIndex: 1000
            }}>
              ❌ WebGL 不支持
              <br />
              <span style={{ fontSize: '1rem', marginTop: '10px', display: 'block' }}>
                您的浏览器不支持WebGL，无法显示3D场景
              </span>
            </div>
          ) : (
            <>
              {/* AR/VR 按钮 */}
              <div style={{
                position: 'fixed',
                top: '20px',
                right: selectedPOI ? '340px' : '20px',
                zIndex: 1000,
                display: 'flex',
                gap: '10px',
                transition: 'right 0.3s ease'
              }}>
                <button
                  style={{
                    padding: '12px 20px',
                    background: xrSupport?.ar ? 'rgba(76, 175, 80, 0.9)' : 'rgba(255, 255, 255, 0.9)',
                    border: 'none',
                    borderRadius: '25px',
                    fontSize: '14px',
                    fontWeight: 'bold',
                    color: xrSupport?.ar ? '#fff' : '#333',
                    cursor: 'pointer',
                    backdropFilter: 'blur(10px)',
                    boxShadow: '0 4px 15px rgba(0, 0, 0, 0.1)'
                  }}
                  onClick={() => {
                    if (xrSupport?.ar) {
                      alert('🎉 AR功能可用！\n\n使用说明：\n• 允许摄像头权限\n• 在光线充足的环境中\n• 将设备对准平面\n• 移动设备查找平面')
                    } else {
                      // 提供AR模拟选项
                      const useSimulation = confirm(`❌ 真实AR功能不可用\n\n检测结果：${xrSupport?.reason || '检测中...'}\n\n是否启用AR模拟模式？\n• 模拟AR视角和交互\n• 体验AR功能演示\n• 适合功能预览和测试`)

                      if (useSimulation) {
                        setArSimulationMode(true)
                        alert('🎮 AR模拟模式已启用！\n\n模拟功能：\n• AR视角相机位置\n• 增强的POI显示\n• 模拟AR交互体验\n\n这是AR功能的演示版本')
                      } else {
                        let message = `\n\n📱 设备信息：`
                        if (xrSupport?.browser) {
                          const b = xrSupport.browser
                          message += `\n• 系统：${b.isIOS ? 'iOS' : b.isAndroid ? 'Android' : '桌面'}`
                          message += `\n• 浏览器：${b.isChrome ? 'Chrome' : b.isSafari ? 'Safari' : '其他'}`
                          if (b.chromeVersion) message += ` ${b.chromeVersion}`
                          if (b.iosVersion) message += `\n• iOS版本：${b.iosVersion}`
                          if (b.androidVersion) message += `\n• Android版本：${b.androidVersion}`
                        }

                        if (xrSupport?.deviceIssues?.length > 0) {
                          message += `\n\n🔧 需要满足：\n• ${xrSupport.deviceIssues.join('\n• ')}`
                        }

                        message += '\n\n✅ AR支持要求：\n• iOS 14.3+ (Chrome 94+)\n• Android 7.0+ (Chrome 79+)\n• 支持ARCore/ARKit的设备'

                        alert(message)
                      }
                    }
                  }}
                >
                  {xrSupport?.ar ? '✅ AR模式' : '❌ AR模式'}
                </button>
                <button
                  style={{
                    padding: '12px 20px',
                    background: xrSupport?.vr ? 'rgba(76, 175, 80, 0.9)' : 'rgba(255, 255, 255, 0.9)',
                    border: 'none',
                    borderRadius: '25px',
                    fontSize: '14px',
                    fontWeight: 'bold',
                    color: xrSupport?.vr ? '#fff' : '#333',
                    cursor: 'pointer',
                    backdropFilter: 'blur(10px)',
                    boxShadow: '0 4px 15px rgba(0, 0, 0, 0.1)'
                  }}
                  onClick={() => {
                    if (xrSupport?.vr) {
                      alert('VR功能可用！点击后将启动VR模式。\n\n请确保：\n• VR头显已连接\n• 头显驱动程序已安装\n• 浏览器允许VR权限')
                    } else {
                      alert(`VR功能不可用\n\n原因：${xrSupport?.reason || '检测中...'}\n\n支持的设备：\n• Meta Quest系列\n• HTC Vive系列\n• Valve Index\n• Windows Mixed Reality`)
                    }
                  }}
                >
                  {xrSupport?.vr ? '✅ VR模式' : '❌ VR模式'}
                </button>
              </div>

              {/* Safari用户提示 */}
              {xrSupport?.browser?.isSafari && (
                <div style={{
                  position: 'fixed',
                  top: '80px',
                  left: '20px',
                  right: '20px',
                  background: 'rgba(255, 193, 7, 0.9)',
                  color: '#333',
                  padding: '15px',
                  borderRadius: '10px',
                  fontSize: '14px',
                  zIndex: 1000,
                  textAlign: 'center'
                }}>
                  🍎 <strong>Safari用户提示</strong><br />
                  Safari不支持WebXR AR功能<br />
                  请使用 <strong>Chrome浏览器</strong> 获得完整AR体验
                </div>
              )}

              {/* 信息面板 */}
              <InfoPanel
                poi={selectedPOI}
                onClose={() => setSelectedPOI(null)}
              />

              {/* 操作提示 */}
              <div style={{
                position: 'fixed',
                bottom: '20px',
                right: '20px',
                background: 'rgba(0, 0, 0, 0.7)',
                backdropFilter: 'blur(10px)',
                padding: '15px',
                borderRadius: '15px',
                color: 'white',
                fontSize: '12px',
                zIndex: 1000,
                maxWidth: '200px'
              }}>
                <div style={{ marginBottom: '8px', fontWeight: 'bold' }}>🎮 操作指南</div>
                <div>• 拖拽：旋转视角</div>
                <div>• 滚轮：缩放场景</div>
                <div>• 点击球体：查看详情</div>
              </div>

              {/* 状态指示器 */}
              <div style={{
                position: 'fixed',
                bottom: '20px',
                left: '20px',
                background: arSimulationMode ? 'rgba(255, 152, 0, 0.9)' : 'rgba(78, 205, 196, 0.9)',
                backdropFilter: 'blur(10px)',
                padding: '10px 15px',
                borderRadius: '20px',
                color: 'white',
                fontSize: '14px',
                fontWeight: '500',
                zIndex: 1000,
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                cursor: arSimulationMode ? 'pointer' : 'default'
              }}
              onClick={arSimulationMode ? () => {
                if (confirm('退出AR模拟模式？')) {
                  setArSimulationMode(false)
                }
              } : undefined}
              >
                <div style={{
                  width: '8px',
                  height: '8px',
                  background: '#ffffff',
                  borderRadius: '50%',
                  animation: 'pulse 2s infinite'
                }}></div>
                {arSimulationMode ? '🎮 AR 模拟模式 (点击退出)' : 'AR 就绪'}
              </div>

              <Canvas
                camera={{
                  position: arSimulationMode ? [0, 2, 5] : [0, 5, 10],
                  fov: arSimulationMode ? 75 : 60
                }}
                style={{ height: '100vh', width: '100vw' }}
                onCreated={() => console.log('Canvas created successfully')}
                onError={(error) => console.error('Canvas error:', error)}
              >
                <BasicScene
                  selectedPOI={selectedPOI}
                  onPOIClick={handlePOIClick}
                  arSimulationMode={arSimulationMode}
                />
              </Canvas>
            </>
          )}
        </>
      )}
    </div>
  )
}

export default App
