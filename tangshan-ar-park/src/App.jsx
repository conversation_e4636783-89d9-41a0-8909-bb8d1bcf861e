import React, { Suspense, useState } from 'react'
import { Canvas } from '@react-three/fiber'
import { OrbitControls, Text } from '@react-three/drei'
import './App.css'

// 简单的3D场景组件
function BasicScene() {
  return (
    <>
      {/* 基础光照 */}
      <ambientLight intensity={0.5} />
      <directionalLight position={[10, 10, 5]} intensity={1} />

      {/* 地面 */}
      <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -2, 0]}>
        <planeGeometry args={[20, 20]} />
        <meshStandardMaterial color="#8B4513" />
      </mesh>

      {/* 矿坑水面 */}
      <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -1.8, 0]}>
        <circleGeometry args={[6, 32]} />
        <meshStandardMaterial color="#006994" transparent opacity={0.8} />
      </mesh>

      {/* POI标记 - 主矿坑 */}
      <mesh position={[0, 0, 0]}>
        <sphereGeometry args={[0.3, 16, 16]} />
        <meshStandardMaterial color="#ff6b6b" />
      </mesh>

      {/* POI标记 - 观景台 */}
      <mesh position={[8, 2, -5]}>
        <sphereGeometry args={[0.3, 16, 16]} />
        <meshStandardMaterial color="#4ecdc4" />
      </mesh>

      {/* POI标记 - 生态步道 */}
      <mesh position={[-6, 0, 3]}>
        <sphereGeometry args={[0.3, 16, 16]} />
        <meshStandardMaterial color="#45b7d1" />
      </mesh>

      {/* 场景标题 */}
      <Text
        position={[0, 8, -10]}
        fontSize={1}
        color="#ffffff"
        anchorX="center"
        anchorY="middle"
      >
        汤山矿坑公园 AR 体验
      </Text>

      {/* 相机控制 */}
      <OrbitControls
        enablePan={true}
        enableZoom={true}
        enableRotate={true}
        maxPolarAngle={Math.PI / 2}
      />
    </>
  )
}

function App() {
  const [showTest, setShowTest] = useState(true)
  return (
    <div className="app">
      {/* 切换按钮 */}
      <button
        onClick={() => setShowTest(!showTest)}
        style={{
          position: 'fixed',
          top: '20px',
          left: '20px',
          zIndex: 1000,
          padding: '10px 20px',
          background: 'rgba(78, 205, 196, 0.9)',
          color: 'white',
          border: 'none',
          borderRadius: '25px',
          cursor: 'pointer',
          fontSize: '14px',
          fontWeight: 'bold'
        }}
      >
        {showTest ? '🎮 启用3D场景' : '📋 返回测试页面'}
      </button>

      {showTest ? (
        // 测试页面
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          color: 'white',
          fontSize: '2rem',
          textAlign: 'center',
          background: 'rgba(0,0,0,0.7)',
          padding: '20px',
          borderRadius: '10px',
          zIndex: 1000
        }}>
          🏔️ 汤山矿坑公园 AR 互动平台 🏔️
          <br />
          <span style={{ fontSize: '1rem', marginTop: '10px', display: 'block' }}>
            ✅ 测试版本 - 基础功能正常
          </span>
          <div style={{
            fontSize: '0.8rem',
            marginTop: '15px',
            color: '#4ecdc4',
            display: 'flex',
            flexDirection: 'column',
            gap: '5px'
          }}>
            <div>🎮 React 应用正常运行</div>
            <div>🌐 开发服务器连接正常</div>
            <div>📱 移动端兼容测试通过</div>
            <div style={{ marginTop: '10px', color: '#feca57' }}>
              点击左上角按钮体验3D场景 →
            </div>
          </div>
        </div>
      ) : (
        // 3D场景
        <Canvas
          camera={{ position: [0, 5, 10], fov: 60 }}
          style={{ height: '100vh', width: '100vw' }}
        >
          <Suspense fallback={null}>
            <BasicScene />
          </Suspense>
        </Canvas>
      )}
    </div>
  )
}

export default App
