import React, { Suspense, useState } from 'react'
import { Canvas } from '@react-three/fiber'
import { OrbitControls, Text } from '@react-three/drei'
import Navigation from './components/Navigation'
import './App.css'

// 简化版汤山场景
function TangshanSceneSimple({ scene }) {
  return (
    <>
      {/* 基础光照 */}
      <ambientLight intensity={0.5} />
      <directionalLight position={[10, 10, 5]} intensity={1} />

      {/* 地面 */}
      <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -2, 0]}>
        <planeGeometry args={[20, 20]} />
        <meshStandardMaterial color="#8B4513" />
      </mesh>

      {/* 矿坑水面 */}
      <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -1.8, 0]}>
        <circleGeometry args={[6, 32]} />
        <meshStandardMaterial color="#006994" transparent opacity={0.8} />
      </mesh>

      {/* POI标记 - 主矿坑 */}
      <mesh position={[0, 0, 0]}>
        <sphereGeometry args={[0.3, 16, 16]} />
        <meshStandardMaterial color="#ff6b6b" />
      </mesh>

      {/* POI标记 - 观景台 */}
      <mesh position={[8, 2, -5]}>
        <sphereGeometry args={[0.3, 16, 16]} />
        <meshStandardMaterial color="#4ecdc4" />
      </mesh>

      {/* POI标记 - 生态步道 */}
      <mesh position={[-6, 0, 3]}>
        <sphereGeometry args={[0.3, 16, 16]} />
        <meshStandardMaterial color="#45b7d1" />
      </mesh>

      {/* 场景标题 */}
      <Text
        position={[0, 8, -10]}
        fontSize={1}
        color="#ffffff"
        anchorX="center"
        anchorY="middle"
      >
        汤山矿坑公园 AR 体验
      </Text>

      {/* 场景描述 */}
      <Text
        position={[0, 6.5, -10]}
        fontSize={0.4}
        color="#cccccc"
        anchorX="center"
        anchorY="middle"
        maxWidth={15}
      >
        {scene === 'overview' && '探索这个从废弃采石场转变为生态公园的奇迹'}
        {scene === 'history' && '回顾汤山采石的历史变迁'}
        {scene === 'nature' && '发现生态修复后的自然奇观'}
      </Text>

      {/* 相机控制 */}
      <OrbitControls
        enablePan={true}
        enableZoom={true}
        enableRotate={true}
        maxPolarAngle={Math.PI / 2}
      />
    </>
  )
}

function SimpleApp() {
  const [currentScene, setCurrentScene] = useState('overview')

  return (
    <div className="app">
      {/* 导航菜单 */}
      <Navigation
        currentScene={currentScene}
        onSceneChange={setCurrentScene}
      />

      <Canvas
        camera={{ position: [0, 5, 10], fov: 60 }}
        style={{ height: '100vh', width: '100vw' }}
      >
        <Suspense fallback={null}>
          <TangshanSceneSimple scene={currentScene} />
        </Suspense>
      </Canvas>
    </div>
  )
}

function App() {
  return <SimpleApp />
}

export default App
