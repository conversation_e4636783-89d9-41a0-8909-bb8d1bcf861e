import React, { Suspense, useState, useEffect } from 'react'
import { Canvas, useLoader } from '@react-three/fiber'
import { OrbitControls, Text, useGLTF, Html } from '@react-three/drei'
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader'
import * as THREE from 'three'
import './App.css'



// 超安全的OBJ加载组件
function SimpleOBJLoader({ url, position = [0, 0, 0], scale = 1, rotation = [0, 0, 0] }) {
  const [loadingState, setLoadingState] = useState('loading') // 'loading', 'success', 'error'
  const [objModel, setObjModel] = useState(null)
  const [errorMessage, setErrorMessage] = useState('')

  useEffect(() => {
    console.log(`🔄 Starting to load OBJ: ${url}`)
    setLoadingState('loading')
    setErrorMessage('')

    // 添加超时保护
    const timeoutId = setTimeout(() => {
      console.error(`⏰ OBJ loading timeout for: ${url}`)
      setLoadingState('error')
      setErrorMessage('加载超时')
    }, 10000) // 10秒超时

    try {
      const loader = new OBJLoader()

      loader.load(
        url,
        // 成功回调
        (object) => {
          clearTimeout(timeoutId)
          console.log(`✅ OBJ loaded successfully:`, object)

          // 验证对象是否有效
          if (!object || !object.children || object.children.length === 0) {
            console.error(`❌ Invalid OBJ object:`, object)
            setLoadingState('error')
            setErrorMessage('无效的OBJ文件')
            return
          }

          setObjModel(object)
          setLoadingState('success')
        },
        // 进度回调
        (progress) => {
          console.log(`📊 Loading progress:`, progress)
        },
        // 错误回调
        (error) => {
          clearTimeout(timeoutId)
          console.error(`❌ Failed to load OBJ:`, error)
          setLoadingState('error')
          setErrorMessage(error.message || '加载失败')
        }
      )
    } catch (error) {
      clearTimeout(timeoutId)
      console.error(`❌ Critical error in OBJ loading:`, error)
      setLoadingState('error')
      setErrorMessage('严重错误')
    }

    // 清理函数
    return () => {
      clearTimeout(timeoutId)
    }
  }, [url])

  // 显示加载状态
  if (loadingState === 'loading') {
    return (
      <group position={position}>
        <mesh scale={scale} rotation={rotation}>
          <boxGeometry args={[1, 1, 1]} />
          <meshStandardMaterial color="#ffc107" />
        </mesh>
        <Html position={[0, 1.5, 0]}>
          <div style={{
            background: 'rgba(255, 193, 7, 0.9)',
            color: 'white',
            padding: '5px 10px',
            borderRadius: '5px',
            fontSize: '12px',
            whiteSpace: 'nowrap',
            pointerEvents: 'none',
            textAlign: 'center'
          }}>
            🔄 加载中...<br />
            <small>{url.split('/').pop()}</small>
          </div>
        </Html>
      </group>
    )
  }

  // 显示错误状态
  if (loadingState === 'error') {
    return (
      <group position={position}>
        <mesh scale={scale} rotation={rotation}>
          <boxGeometry args={[2, 2, 2]} />
          <meshStandardMaterial color="#dc3545" />
        </mesh>
        <Html position={[0, 2, 0]}>
          <div style={{
            background: 'rgba(220, 53, 69, 0.9)',
            color: 'white',
            padding: '5px 10px',
            borderRadius: '5px',
            fontSize: '12px',
            whiteSpace: 'nowrap',
            pointerEvents: 'none',
            textAlign: 'center'
          }}>
            ❌ {errorMessage}<br />
            <small>{url.split('/').pop()}</small>
          </div>
        </Html>
      </group>
    )
  }

  // 显示成功加载的模型
  if (loadingState === 'success' && objModel) {
    return (
      <group position={position}>
        <primitive
          object={objModel}
          scale={scale}
          rotation={rotation}
        />
        <Html position={[0, 3, 0]}>
          <div style={{
            background: 'rgba(40, 167, 69, 0.9)',
            color: 'white',
            padding: '5px 10px',
            borderRadius: '5px',
            fontSize: '12px',
            whiteSpace: 'nowrap',
            pointerEvents: 'none',
            textAlign: 'center'
          }}>
            ✅ OBJ模型<br />
            <small>{url.split('/').pop()}</small>
          </div>
        </Html>
      </group>
    )
  }

  return null
}

// 大文件OBJ加载器
function LargeOBJLoader({ url, position = [0, 0, 0], scale = 1, rotation = [0, 0, 0] }) {
  const [loadingState, setLoadingState] = useState('ready') // 'ready', 'loading', 'success', 'error'
  const [loadingProgress, setLoadingProgress] = useState(0)
  const [objModel, setObjModel] = useState(null)
  const [fileSize, setFileSize] = useState(0)

  // 获取文件大小
  useEffect(() => {
    fetch(url, { method: 'HEAD' })
      .then(response => {
        const size = response.headers.get('content-length')
        if (size) {
          setFileSize(parseInt(size))
          console.log(`📊 OBJ file size: ${(parseInt(size) / 1024 / 1024).toFixed(2)} MB`)
        }
      })
      .catch(error => {
        console.warn('Could not get file size:', error)
      })
  }, [url])

  const startLoading = () => {
    console.log(`🔄 Starting large OBJ loading: ${url}`)
    setLoadingState('loading')
    setLoadingProgress(0)

    const loader = new OBJLoader()

    // 设置较长的超时时间（60秒）
    const timeoutId = setTimeout(() => {
      console.error(`⏰ Large OBJ loading timeout: ${url}`)
      setLoadingState('error')
    }, 60000)

    loader.load(
      url,
      // 成功回调
      (object) => {
        clearTimeout(timeoutId)
        console.log(`✅ Large OBJ loaded successfully`)

        // 简化模型以提高性能
        let totalFaces = 0
        object.traverse((child) => {
          if (child.isMesh) {
            if (child.geometry) {
              const faces = child.geometry.index ? child.geometry.index.count / 3 : child.geometry.attributes.position.count / 3
              totalFaces += faces

              // 为大模型添加简单材质
              child.material = new THREE.MeshLambertMaterial({
                color: '#8B4513',
                side: THREE.DoubleSide
              })
            }
          }
        })

        console.log(`📊 Total faces: ${totalFaces}`)

        if (totalFaces > 100000) {
          console.warn(`⚠️ High polygon count: ${totalFaces} faces`)
        }

        setObjModel(object)
        setLoadingState('success')
      },
      // 进度回调
      (progress) => {
        if (progress.lengthComputable) {
          const percent = (progress.loaded / progress.total) * 100
          setLoadingProgress(percent)
          console.log(`📊 Loading progress: ${percent.toFixed(1)}%`)
        }
      },
      // 错误回调
      (error) => {
        clearTimeout(timeoutId)
        console.error(`❌ Failed to load large OBJ:`, error)
        setLoadingState('error')
      }
    )
  }

  // 准备状态
  if (loadingState === 'ready') {
    return (
      <group position={position}>
        <mesh
          scale={scale}
          rotation={rotation}
          onClick={startLoading}
          onPointerOver={(e) => {
            e.object.material.emissive.setHex(0x444444)
            document.body.style.cursor = 'pointer'
          }}
          onPointerOut={(e) => {
            e.object.material.emissive.setHex(0x000000)
            document.body.style.cursor = 'default'
          }}
        >
          <boxGeometry args={[2, 2, 2]} />
          <meshStandardMaterial color="#17a2b8" />
        </mesh>

        <Html position={[0, 2, 0]}>
          <div style={{
            background: 'rgba(23, 162, 184, 0.9)',
            color: 'white',
            padding: '8px 12px',
            borderRadius: '8px',
            fontSize: '12px',
            whiteSpace: 'nowrap',
            pointerEvents: 'none',
            textAlign: 'center'
          }}>
            📦 点击加载大型OBJ<br />
            <small>{url.split('/').pop()}</small>
            {fileSize > 0 && (
              <div style={{ fontSize: '10px', marginTop: '2px' }}>
                大小: {(fileSize / 1024 / 1024).toFixed(2)} MB
              </div>
            )}
          </div>
        </Html>
      </group>
    )
  }

  // 加载状态
  if (loadingState === 'loading') {
    return (
      <group position={position}>
        <mesh scale={scale} rotation={rotation}>
          <boxGeometry args={[2, 2, 2]} />
          <meshStandardMaterial color="#ffc107" />
        </mesh>

        <Html position={[0, 2, 0]}>
          <div style={{
            background: 'rgba(255, 193, 7, 0.9)',
            color: 'white',
            padding: '8px 12px',
            borderRadius: '8px',
            fontSize: '12px',
            whiteSpace: 'nowrap',
            pointerEvents: 'none',
            textAlign: 'center'
          }}>
            🔄 加载中...<br />
            <div style={{
              width: '100px',
              height: '4px',
              background: 'rgba(255,255,255,0.3)',
              borderRadius: '2px',
              margin: '4px auto',
              overflow: 'hidden'
            }}>
              <div style={{
                width: `${loadingProgress}%`,
                height: '100%',
                background: 'white',
                transition: 'width 0.3s ease'
              }} />
            </div>
            <small>{loadingProgress.toFixed(1)}%</small>
          </div>
        </Html>
      </group>
    )
  }

  // 错误状态
  if (loadingState === 'error') {
    return (
      <group position={position}>
        <mesh
          scale={scale}
          rotation={rotation}
          onClick={() => setLoadingState('ready')}
          onPointerOver={(e) => {
            e.object.material.emissive.setHex(0x444444)
            document.body.style.cursor = 'pointer'
          }}
          onPointerOut={(e) => {
            e.object.material.emissive.setHex(0x000000)
            document.body.style.cursor = 'default'
          }}
        >
          <boxGeometry args={[2, 2, 2]} />
          <meshStandardMaterial color="#dc3545" />
        </mesh>

        <Html position={[0, 2, 0]}>
          <div style={{
            background: 'rgba(220, 53, 69, 0.9)',
            color: 'white',
            padding: '8px 12px',
            borderRadius: '8px',
            fontSize: '12px',
            whiteSpace: 'nowrap',
            pointerEvents: 'none',
            textAlign: 'center'
          }}>
            ❌ 加载失败<br />
            <small>点击重试</small>
          </div>
        </Html>
      </group>
    )
  }

  // 成功状态
  if (loadingState === 'success' && objModel) {
    return (
      <group position={position}>
        <primitive
          object={objModel}
          scale={scale}
          rotation={rotation}
        />
        <Html position={[0, 4, 0]}>
          <div style={{
            background: 'rgba(40, 167, 69, 0.9)',
            color: 'white',
            padding: '5px 10px',
            borderRadius: '5px',
            fontSize: '12px',
            whiteSpace: 'nowrap',
            pointerEvents: 'none',
            textAlign: 'center'
          }}>
            ✅ 大型OBJ模型<br />
            <small>{url.split('/').pop()}</small>
          </div>
        </Html>
      </group>
    )
  }

  return null
}

// 完全安全的OBJ模型组件 - 禁用所有OBJ加载
function OBJModel({ url, position = [0, 0, 0], scale = 1, rotation = [0, 0, 0] }) {
  console.log(`�️ OBJ Model component called for: ${url} - COMPLETELY DISABLED`)

  // 完全禁用OBJ加载，显示问题诊断信息
  return (
    <group position={position}>
      <mesh
        scale={scale}
        rotation={rotation}
        onClick={() => {
          alert(`OBJ文件问题诊断\n\n文件: ${url.split('/').pop()}\n\n可能的问题:\n• 文件格式不标准\n• 文件过大或损坏\n• 包含不支持的特性\n\n建议解决方案:\n• 转换为GLB格式\n• 简化模型复杂度\n• 检查文件完整性`)
        }}
        onPointerOver={(e) => {
          e.object.material.emissive.setHex(0x444444)
          document.body.style.cursor = 'pointer'
        }}
        onPointerOut={(e) => {
          e.object.material.emissive.setHex(0x000000)
          document.body.style.cursor = 'default'
        }}
      >
        <boxGeometry args={[2, 2, 2]} />
        <meshStandardMaterial color="#dc3545" />
      </mesh>

      <Html position={[0, 2, 0]}>
        <div style={{
          background: 'rgba(220, 53, 69, 0.9)',
          color: 'white',
          padding: '8px 12px',
          borderRadius: '8px',
          fontSize: '12px',
          whiteSpace: 'nowrap',
          pointerEvents: 'none',
          textAlign: 'center'
        }}>
          ⚠️ OBJ有问题<br />
          <small>点击查看诊断</small>
        </div>
      </Html>
    </group>
  )

  // 显示安全的备用几何体，禁用OBJ加载
  return (
    <group position={position}>
      {/* 安全的备用几何体 */}
      <mesh
        scale={scale}
        rotation={rotation}
        onClick={() => {
          console.log(`🛡️ OBJ loading disabled for safety: ${url}`)
          alert('OBJ加载已禁用以防止页面崩溃\n\n您的OBJ文件可能有格式问题\n请检查文件是否为标准OBJ格式')
        }}
        onPointerOver={(e) => {
          e.object.material.emissive.setHex(0x444444)
          document.body.style.cursor = 'pointer'
        }}
        onPointerOut={(e) => {
          e.object.material.emissive.setHex(0x000000)
          document.body.style.cursor = 'default'
        }}
      >
        <boxGeometry args={[2, 2, 2]} />
        <meshStandardMaterial color="#6c757d" />
      </mesh>

      {/* 标签显示OBJ加载已禁用 */}
      <Html position={[0, 2, 0]}>
        <div style={{
          background: 'rgba(108, 117, 125, 0.9)',
          color: 'white',
          padding: '5px 10px',
          borderRadius: '5px',
          fontSize: '12px',
          whiteSpace: 'nowrap',
          pointerEvents: 'none',
          textAlign: 'center'
        }}>
          �️ OBJ已禁用<br />
          <small>{url.split('/').pop()}</small>
        </div>
      </Html>
    </group>
  )
}

// GLTF模型组件
function GLTFModel({ url, position = [0, 0, 0], scale = 1, rotation = [0, 0, 0] }) {
  try {
    const { scene } = useGLTF(url)
    return (
      <primitive
        object={scene}
        position={position}
        scale={scale}
        rotation={rotation}
      />
    )
  } catch (error) {
    console.warn(`Failed to load GLTF model: ${url}`, error)
    return (
      <mesh position={position}>
        <boxGeometry args={[1, 1, 1]} />
        <meshStandardMaterial color="#ff6b6b" />
      </mesh>
    )
  }
}

// 统一3D模型组件 - 自动检测格式
function Model3D({ url, mtlUrl, position = [0, 0, 0], scale = 1, rotation = [0, 0, 0] }) {
  // 根据文件扩展名判断模型格式
  const fileExtension = url.split('.').pop().toLowerCase()

  if (fileExtension === 'obj') {
    return (
      <OBJModel
        url={url}
        mtlUrl={mtlUrl}
        position={position}
        scale={scale}
        rotation={rotation}
      />
    )
  } else if (fileExtension === 'glb' || fileExtension === 'gltf') {
    return (
      <GLTFModel
        url={url}
        position={position}
        scale={scale}
        rotation={rotation}
      />
    )
  } else {
    console.warn(`Unsupported model format: ${fileExtension}`)
    return (
      <mesh position={position}>
        <boxGeometry args={[1, 1, 1]} />
        <meshStandardMaterial color="#ff6b6b" />
      </mesh>
    )
  }
}

// 加载状态组件
function LoadingFallback({ position }) {
  return (
    <mesh position={position}>
      <boxGeometry args={[0.5, 0.5, 0.5]} />
      <meshStandardMaterial color="#cccccc" />
    </mesh>
  )
}

// POI数据 - 支持GLB和OBJ格式
const POI_DATA = [
  {
    id: 'main-pit',
    name: '主矿坑',
    position: [0, 0, 0],
    color: '#ff6b6b',
    description: '汤山矿坑公园的核心景观，深达数十米的废弃采石坑，现已改造为生态景观湖。',
    details: '深度约40米，面积约15公顷，开采时间1950-2010年',
    model: {
      // OBJ格式配置示例 - 测试版本
      url: '/models/obj/kkgy.obj',        // OBJ文件路径
      mtlUrl: '/models/obj/kkgy.mtl',     // MTL材质文件路径（必需）
      scale: 2,                               // 放大2倍便于观察
      rotation: [0, 0, 0],                   // 旋转角度 [x, y, z]
      fallback: 'pit'                        // 备用几何体类型
    }
  },
  {
    id: 'observation-deck',
    name: '观景台',
    position: [8, 2, -5],
    color: '#4ecdc4',
    description: '最佳观赏矿坑全景的位置，可以俯瞰整个矿坑湖和周围的生态恢复区域。',
    details: '高度海拔120米，360度全景视野，最佳观赏时间日出日落',
    model: {
      // GLB格式配置示例（对比）
      url: '/models/glb/observation-deck.glb', // GLB文件路径
      scale: 0.5,                              // 缩放比例
      rotation: [0, Math.PI / 4, 0],          // Y轴旋转45度
      fallback: 'tower'                        // 备用几何体类型
      // 注意：GLB格式不需要mtlUrl参数
    }
  },
  {
    id: 'eco-trail',
    name: '生态步道',
    position: [-6, 0, 3],
    color: '#45b7d1',
    description: '环绕矿坑的生态步道，展示了从工业废地到生态公园的转变过程。',
    details: '总长度2.5公里，步行时间约45分钟，8个观景点',
    model: {
      // OBJ格式高级配置示例
      url: '/models/obj/eco-trail.obj',       // OBJ文件路径
      mtlUrl: '/models/obj/eco-trail.mtl',    // MTL材质文件路径
      scale: 0.8,                             // 缩放比例
      rotation: [0, -Math.PI / 6, 0],        // Y轴旋转-30度
      fallback: 'path'                        // 备用几何体类型
    }
  }
]

// 替代几何体生成器
function createFallbackGeometry(type, position) {
  switch (type) {
    case 'pit':
      return (
        <group position={position}>
          {/* 矿坑边缘 */}
          <mesh position={[0, -0.5, 0]}>
            <cylinderGeometry args={[3, 2, 1, 16]} />
            <meshStandardMaterial color="#8B4513" />
          </mesh>
          {/* 水面 */}
          <mesh position={[0, -1, 0]}>
            <cylinderGeometry args={[2, 2, 0.1, 16]} />
            <meshStandardMaterial color="#006994" transparent opacity={0.8} />
          </mesh>
        </group>
      )
    case 'tower':
      return (
        <group position={position}>
          {/* 观景台基座 */}
          <mesh position={[0, 0, 0]}>
            <cylinderGeometry args={[0.8, 1, 2, 8]} />
            <meshStandardMaterial color="#666666" />
          </mesh>
          {/* 观景台顶部 */}
          <mesh position={[0, 1.5, 0]}>
            <cylinderGeometry args={[1.2, 0.8, 0.5, 8]} />
            <meshStandardMaterial color="#4ecdc4" />
          </mesh>
        </group>
      )
    case 'path':
      return (
        <group position={position}>
          {/* 步道路径 */}
          <mesh position={[0, 0, 0]} rotation={[0, 0, 0]}>
            <boxGeometry args={[4, 0.1, 1]} />
            <meshStandardMaterial color="#8B7355" />
          </mesh>
          {/* 步道标识 */}
          <mesh position={[0, 0.5, 0]}>
            <boxGeometry args={[0.2, 1, 0.2]} />
            <meshStandardMaterial color="#45b7d1" />
          </mesh>
        </group>
      )
    default:
      return (
        <mesh position={position}>
          <boxGeometry args={[1, 1, 1]} />
          <meshStandardMaterial color="#ff6b6b" />
        </mesh>
      )
  }
}

// 增强的POI组件 - 支持3D模型
function POIComponent({ poi, onClick, isSelected, use3DModels = false }) {
  const markerPosition = [poi.position[0], poi.position[1] + 2, poi.position[2]]

  return (
    <group>
      {/* 3D模型或替代几何体 */}
      {use3DModels && poi.model ? (
        <Suspense fallback={<LoadingFallback position={poi.position} />}>
          <Model3D
            url={poi.model.url}
            mtlUrl={poi.model.mtlUrl}  // 传递MTL材质文件URL
            position={poi.position}
            scale={poi.model.scale}
            rotation={poi.model.rotation}
          />
        </Suspense>
      ) : (
        createFallbackGeometry(poi.model?.fallback || 'default', poi.position)
      )}

      {/* POI标记球体 */}
      <mesh
        position={markerPosition}
        onClick={() => onClick(poi)}
        onPointerOver={(e) => {
          e.object.material.emissive.setHex(0x444444)
          document.body.style.cursor = 'pointer'
        }}
        onPointerOut={(e) => {
          e.object.material.emissive.setHex(isSelected ? 0x222222 : 0x000000)
          document.body.style.cursor = 'default'
        }}
      >
        <sphereGeometry args={[isSelected ? 0.4 : 0.3, 16, 16]} />
        <meshStandardMaterial
          color={poi.color}
          emissive={isSelected ? poi.color : '#000000'}
          emissiveIntensity={isSelected ? 0.3 : 0}
        />
      </mesh>

      {/* POI标签 */}
      {isSelected && (
        <Html position={[markerPosition[0], markerPosition[1] + 0.8, markerPosition[2]]}>
          <div style={{
            background: 'rgba(0,0,0,0.8)',
            color: 'white',
            padding: '5px 10px',
            borderRadius: '5px',
            fontSize: '12px',
            whiteSpace: 'nowrap',
            pointerEvents: 'none'
          }}>
            {poi.name}
          </div>
        </Html>
      )}
    </group>
  )
}

// 信息面板组件
function InfoPanel({ poi, onClose }) {
  if (!poi) return null

  return (
    <div style={{
      position: 'fixed',
      top: '20px',
      right: '20px',
      width: '300px',
      background: 'rgba(255, 255, 255, 0.95)',
      backdropFilter: 'blur(10px)',
      borderRadius: '15px',
      padding: '20px',
      boxShadow: '0 10px 30px rgba(0,0,0,0.3)',
      zIndex: 1000,
      color: '#333'
    }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
        <h3 style={{ margin: 0, color: poi.color, fontSize: '1.2rem' }}>{poi.name}</h3>
        <button
          onClick={onClose}
          style={{
            background: 'none',
            border: 'none',
            fontSize: '20px',
            cursor: 'pointer',
            color: '#666'
          }}
        >
          ✕
        </button>
      </div>
      <p style={{ margin: '0 0 10px 0', lineHeight: '1.5', fontSize: '14px' }}>
        {poi.description}
      </p>
      <div style={{
        background: 'rgba(78, 205, 196, 0.1)',
        padding: '10px',
        borderRadius: '8px',
        fontSize: '13px',
        color: '#555'
      }}>
        <strong>详细信息：</strong><br />
        {poi.details}
      </div>
    </div>
  )
}

// 汤山矿坑3D场景
function BasicScene({ selectedPOI, onPOIClick, arSimulationMode, use3DModels = false }) {
  return (
    <>
      {/* 基础光照 */}
      <ambientLight intensity={0.5} />
      <directionalLight position={[10, 10, 5]} intensity={1} />
      <pointLight position={[-10, 10, -10]} intensity={0.5} />

      {/* 地面 */}
      <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -2, 0]}>
        <planeGeometry args={[30, 30]} />
        <meshStandardMaterial color="#8B4513" />
      </mesh>

      {/* 矿坑水面 */}
      <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -1.8, 0]}>
        <circleGeometry args={[8, 32]} />
        <meshStandardMaterial color="#006994" transparent opacity={0.8} />
      </mesh>

      {/* POI组件 */}
      {POI_DATA.map((poi) => (
        <POIComponent
          key={poi.id}
          poi={poi}
          onClick={onPOIClick}
          isSelected={selectedPOI?.id === poi.id}
          use3DModels={use3DModels}
        />
      ))}

      {/* 场景标题 */}
      <Text
        position={[0, 8, -10]}
        fontSize={1}
        color="#ffffff"
        anchorX="center"
        anchorY="middle"
        outlineWidth={0.05}
        outlineColor="#000000"
      >
        汤山矿坑公园 AR 体验
      </Text>

      {/* 场景描述 */}
      <Text
        position={[0, 6.5, -10]}
        fontSize={0.4}
        color="#cccccc"
        anchorX="center"
        anchorY="middle"
        outlineWidth={0.02}
        outlineColor="#000000"
        maxWidth={15}
      >
        {use3DModels ? '🎨 3D模型展示模式' : '探索这个从废弃采石场转变为生态公园的奇迹'}
      </Text>

      {/* 相机控制 */}
      <OrbitControls
        enablePan={true}
        enableZoom={true}
        enableRotate={true}
        maxPolarAngle={Math.PI / 2}
      />
    </>
  )
}

// WebGL检测函数
function checkWebGL() {
  try {
    const canvas = document.createElement('canvas')
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
    return !!gl
  } catch (e) {
    return false
  }
}

// 浏览器和设备检测
function getBrowserInfo() {
  const ua = navigator.userAgent
  const isSafari = /Safari/.test(ua) && !/Chrome/.test(ua)
  const isChrome = /Chrome/.test(ua)
  const isIOS = /iPad|iPhone|iPod/.test(ua)
  const isAndroid = /Android/.test(ua)

  // 获取版本信息
  const chromeVersion = isChrome ? ua.match(/Chrome\/(\d+)/)?.[1] : null
  const iosVersion = isIOS ? ua.match(/OS (\d+)_(\d+)/)?.[1] : null
  const androidVersion = isAndroid ? ua.match(/Android (\d+)/)?.[1] : null

  return {
    isSafari,
    isChrome,
    isIOS,
    isAndroid,
    chromeVersion: chromeVersion ? parseInt(chromeVersion) : null,
    iosVersion: iosVersion ? parseInt(iosVersion) : null,
    androidVersion: androidVersion ? parseInt(androidVersion) : null
  }
}

// 设备AR支持检测
function checkDeviceARSupport(browser) {
  const issues = []

  if (browser.isIOS) {
    if (!browser.iosVersion || browser.iosVersion < 14) {
      issues.push('需要iOS 14.3+')
    }
    if (!browser.isChrome) {
      issues.push('iOS需要使用Chrome浏览器')
    }
    if (browser.isChrome && (!browser.chromeVersion || browser.chromeVersion < 94)) {
      issues.push('需要Chrome 94+')
    }
  }

  if (browser.isAndroid) {
    if (!browser.androidVersion || browser.androidVersion < 7) {
      issues.push('需要Android 7.0+')
    }
    if (!browser.isChrome) {
      issues.push('Android需要使用Chrome浏览器')
    }
    if (browser.isChrome && (!browser.chromeVersion || browser.chromeVersion < 79)) {
      issues.push('需要Chrome 79+')
    }
  }

  if (!browser.isIOS && !browser.isAndroid) {
    issues.push('桌面浏览器不支持移动AR')
  }

  return issues
}

// WebXR支持检测
async function checkWebXRSupport() {
  const browser = getBrowserInfo()
  const deviceIssues = checkDeviceARSupport(browser)

  if (!navigator.xr) {
    let reason = 'WebXR API不可用'
    if (browser.isSafari) {
      reason = 'Safari不支持WebXR'
    } else if (deviceIssues.length > 0) {
      reason = `设备不满足要求: ${deviceIssues.join(', ')}`
    }
    return { ar: false, vr: false, reason, browser, deviceIssues }
  }

  try {
    const arSupported = await navigator.xr.isSessionSupported('immersive-ar')
    const vrSupported = await navigator.xr.isSessionSupported('immersive-vr')

    let reason = '支持'
    if (!arSupported && !vrSupported) {
      if (deviceIssues.length > 0) {
        reason = `设备限制: ${deviceIssues.join(', ')}`
      } else {
        reason = '设备不支持XR或缺少ARCore/ARKit'
      }
    }

    return {
      ar: arSupported,
      vr: vrSupported,
      reason,
      browser,
      deviceIssues
    }
  } catch (e) {
    return {
      ar: false,
      vr: false,
      reason: `检测失败: ${e.message}`,
      browser,
      deviceIssues
    }
  }
}

function App() {
  const [showTest, setShowTest] = useState(true)
  const [selectedPOI, setSelectedPOI] = useState(null)
  const [webglSupported, setWebglSupported] = useState(null)
  const [xrSupport, setXrSupport] = useState(null)
  const [arSimulationMode, setArSimulationMode] = useState(false)
  const [use3DModels, setUse3DModels] = useState(false)

  React.useEffect(() => {
    setWebglSupported(checkWebGL())
    checkWebXRSupport().then(setXrSupport)
  }, [])

  const handlePOIClick = (poi) => {
    setSelectedPOI(selectedPOI?.id === poi.id ? null : poi)
  }
  return (
    <div className="app">
      {/* 切换按钮 */}
      <div style={{
        position: 'fixed',
        top: '20px',
        left: '20px',
        zIndex: 1000,
        display: 'flex',
        gap: '10px'
      }}>
        <button
          onClick={() => setShowTest(!showTest)}
          style={{
            padding: '10px 20px',
            background: 'rgba(78, 205, 196, 0.9)',
            color: 'white',
            border: 'none',
            borderRadius: '25px',
            cursor: 'pointer',
            fontSize: '14px',
            fontWeight: 'bold'
          }}
        >
          {showTest ? '🎮 启用3D场景' : '📋 返回测试页面'}
        </button>

        {!showTest && (
          <button
            onClick={() => {
              console.log(`🔄 Switching 3D models: ${!use3DModels ? 'ON' : 'OFF'}`)
              setUse3DModels(!use3DModels)
            }}
            style={{
              padding: '10px 20px',
              background: use3DModels ? 'rgba(255, 152, 0, 0.9)' : 'rgba(108, 117, 125, 0.9)',
              color: 'white',
              border: 'none',
              borderRadius: '25px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: 'bold'
            }}
          >
            {use3DModels ? '🎨 3D模型模式' : '📦 启用3D模型'}
          </button>
        )}
      </div>

      {showTest ? (
        // 测试页面
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          color: 'white',
          fontSize: '2rem',
          textAlign: 'center',
          background: 'rgba(0,0,0,0.7)',
          padding: '20px',
          borderRadius: '10px',
          zIndex: 1000
        }}>
          🏔️ 汤山矿坑公园 AR 互动平台 🏔️
          <br />
          <span style={{ fontSize: '1rem', marginTop: '10px', display: 'block' }}>
            ✅ 测试版本 - 基础功能正常
          </span>
          <div style={{
            fontSize: '0.8rem',
            marginTop: '15px',
            color: '#4ecdc4',
            display: 'flex',
            flexDirection: 'column',
            gap: '5px'
          }}>
            <div>🎮 React 应用正常运行</div>
            <div>🌐 开发服务器连接正常</div>
            <div>📱 移动端兼容测试通过</div>
            <div style={{ color: webglSupported ? '#4ecdc4' : '#ff6b6b' }}>
              {webglSupported === null ? '🔍 检测WebGL支持...' :
               webglSupported ? '✅ WebGL 支持正常' : '❌ WebGL 不支持'}
            </div>
            <div style={{ color: xrSupport?.ar || xrSupport?.vr ? '#4ecdc4' : '#feca57' }}>
              {xrSupport === null ? '🔍 检测WebXR支持...' :
               xrSupport.ar || xrSupport.vr ?
               `✅ WebXR 支持 (AR:${xrSupport.ar?'✓':'✗'} VR:${xrSupport.vr?'✓':'✗'})` :
               `⚠️ WebXR 不支持`}
            </div>
            {xrSupport?.browser && (
              <div style={{ fontSize: '0.7rem', marginTop: '5px', color: '#999' }}>
                {xrSupport.browser.isIOS && `📱 iOS ${xrSupport.browser.iosVersion || '?'}`}
                {xrSupport.browser.isAndroid && `🤖 Android ${xrSupport.browser.androidVersion || '?'}`}
                {!xrSupport.browser.isIOS && !xrSupport.browser.isAndroid && '💻 桌面'}
                {' • '}
                {xrSupport.browser.isChrome && `Chrome ${xrSupport.browser.chromeVersion || '?'}`}
                {xrSupport.browser.isSafari && 'Safari'}
                {!xrSupport.browser.isChrome && !xrSupport.browser.isSafari && '其他浏览器'}
              </div>
            )}
            <div style={{ marginTop: '10px', color: '#feca57' }}>
              点击左上角按钮体验3D场景 →
            </div>
          </div>
        </div>
      ) : (
        // 3D场景
        <>
          {/* WebGL检测 */}
          {webglSupported === false ? (
            <div style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              color: 'white',
              fontSize: '1.5rem',
              textAlign: 'center',
              background: 'rgba(255, 0, 0, 0.8)',
              padding: '20px',
              borderRadius: '10px',
              zIndex: 1000
            }}>
              ❌ WebGL 不支持
              <br />
              <span style={{ fontSize: '1rem', marginTop: '10px', display: 'block' }}>
                您的浏览器不支持WebGL，无法显示3D场景
              </span>
            </div>
          ) : (
            <>
              {/* AR/VR 按钮 */}
              <div style={{
                position: 'fixed',
                top: '20px',
                right: selectedPOI ? '340px' : '20px',
                zIndex: 1000,
                display: 'flex',
                gap: '10px',
                transition: 'right 0.3s ease'
              }}>
                <button
                  style={{
                    padding: '12px 20px',
                    background: xrSupport?.ar ? 'rgba(76, 175, 80, 0.9)' : 'rgba(255, 255, 255, 0.9)',
                    border: 'none',
                    borderRadius: '25px',
                    fontSize: '14px',
                    fontWeight: 'bold',
                    color: xrSupport?.ar ? '#fff' : '#333',
                    cursor: 'pointer',
                    backdropFilter: 'blur(10px)',
                    boxShadow: '0 4px 15px rgba(0, 0, 0, 0.1)'
                  }}
                  onClick={() => {
                    if (xrSupport?.ar) {
                      alert('🎉 AR功能可用！\n\n使用说明：\n• 允许摄像头权限\n• 在光线充足的环境中\n• 将设备对准平面\n• 移动设备查找平面')
                    } else {
                      // 提供AR模拟选项
                      const useSimulation = confirm(`❌ 真实AR功能不可用\n\n检测结果：${xrSupport?.reason || '检测中...'}\n\n是否启用AR模拟模式？\n• 模拟AR视角和交互\n• 体验AR功能演示\n• 适合功能预览和测试`)

                      if (useSimulation) {
                        setArSimulationMode(true)
                        alert('🎮 AR模拟模式已启用！\n\n模拟功能：\n• AR视角相机位置\n• 增强的POI显示\n• 模拟AR交互体验\n\n这是AR功能的演示版本')
                      } else {
                        let message = `\n\n📱 设备信息：`
                        if (xrSupport?.browser) {
                          const b = xrSupport.browser
                          message += `\n• 系统：${b.isIOS ? 'iOS' : b.isAndroid ? 'Android' : '桌面'}`
                          message += `\n• 浏览器：${b.isChrome ? 'Chrome' : b.isSafari ? 'Safari' : '其他'}`
                          if (b.chromeVersion) message += ` ${b.chromeVersion}`
                          if (b.iosVersion) message += `\n• iOS版本：${b.iosVersion}`
                          if (b.androidVersion) message += `\n• Android版本：${b.androidVersion}`
                        }

                        if (xrSupport?.deviceIssues?.length > 0) {
                          message += `\n\n🔧 需要满足：\n• ${xrSupport.deviceIssues.join('\n• ')}`
                        }

                        message += '\n\n✅ AR支持要求：\n• iOS 14.3+ (Chrome 94+)\n• Android 7.0+ (Chrome 79+)\n• 支持ARCore/ARKit的设备'

                        alert(message)
                      }
                    }
                  }}
                >
                  {xrSupport?.ar ? '✅ AR模式' : '❌ AR模式'}
                </button>
                <button
                  style={{
                    padding: '12px 20px',
                    background: xrSupport?.vr ? 'rgba(76, 175, 80, 0.9)' : 'rgba(255, 255, 255, 0.9)',
                    border: 'none',
                    borderRadius: '25px',
                    fontSize: '14px',
                    fontWeight: 'bold',
                    color: xrSupport?.vr ? '#fff' : '#333',
                    cursor: 'pointer',
                    backdropFilter: 'blur(10px)',
                    boxShadow: '0 4px 15px rgba(0, 0, 0, 0.1)'
                  }}
                  onClick={() => {
                    if (xrSupport?.vr) {
                      alert('VR功能可用！点击后将启动VR模式。\n\n请确保：\n• VR头显已连接\n• 头显驱动程序已安装\n• 浏览器允许VR权限')
                    } else {
                      alert(`VR功能不可用\n\n原因：${xrSupport?.reason || '检测中...'}\n\n支持的设备：\n• Meta Quest系列\n• HTC Vive系列\n• Valve Index\n• Windows Mixed Reality`)
                    }
                  }}
                >
                  {xrSupport?.vr ? '✅ VR模式' : '❌ VR模式'}
                </button>
              </div>

              {/* Safari用户提示 */}
              {xrSupport?.browser?.isSafari && (
                <div style={{
                  position: 'fixed',
                  top: '80px',
                  left: '20px',
                  right: '20px',
                  background: 'rgba(255, 193, 7, 0.9)',
                  color: '#333',
                  padding: '15px',
                  borderRadius: '10px',
                  fontSize: '14px',
                  zIndex: 1000,
                  textAlign: 'center'
                }}>
                  🍎 <strong>Safari用户提示</strong><br />
                  Safari不支持WebXR AR功能<br />
                  请使用 <strong>Chrome浏览器</strong> 获得完整AR体验
                </div>
              )}

              {/* 信息面板 */}
              <InfoPanel
                poi={selectedPOI}
                onClose={() => setSelectedPOI(null)}
              />

              {/* 操作提示 */}
              <div style={{
                position: 'fixed',
                bottom: '20px',
                right: '20px',
                background: 'rgba(0, 0, 0, 0.7)',
                backdropFilter: 'blur(10px)',
                padding: '15px',
                borderRadius: '15px',
                color: 'white',
                fontSize: '12px',
                zIndex: 1000,
                maxWidth: '200px'
              }}>
                <div style={{ marginBottom: '8px', fontWeight: 'bold' }}>🎮 操作指南</div>
                <div>• 拖拽：旋转视角</div>
                <div>• 滚轮：缩放场景</div>
                <div>• 点击球体：查看详情</div>
              </div>

              {/* 状态指示器 */}
              <div style={{
                position: 'fixed',
                bottom: '20px',
                left: '20px',
                background: arSimulationMode ? 'rgba(255, 152, 0, 0.9)' : 'rgba(78, 205, 196, 0.9)',
                backdropFilter: 'blur(10px)',
                padding: '10px 15px',
                borderRadius: '20px',
                color: 'white',
                fontSize: '14px',
                fontWeight: '500',
                zIndex: 1000,
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                cursor: arSimulationMode ? 'pointer' : 'default'
              }}
              onClick={arSimulationMode ? () => {
                if (confirm('退出AR模拟模式？')) {
                  setArSimulationMode(false)
                }
              } : undefined}
              >
                <div style={{
                  width: '8px',
                  height: '8px',
                  background: '#ffffff',
                  borderRadius: '50%',
                  animation: 'pulse 2s infinite'
                }}></div>
                {arSimulationMode ? '🎮 AR 模拟模式 (点击退出)' : 'AR 就绪'}
              </div>

              <Canvas
                camera={{
                  position: arSimulationMode ? [0, 2, 5] : [0, 5, 10],
                  fov: arSimulationMode ? 75 : 60
                }}
                style={{ height: '100vh', width: '100vw' }}
                onCreated={() => console.log('Canvas created successfully')}
                onError={(error) => console.error('Canvas error:', error)}
              >
                <BasicScene
                  selectedPOI={selectedPOI}
                  onPOIClick={handlePOIClick}
                  arSimulationMode={arSimulationMode}
                  use3DModels={use3DModels}
                />
              </Canvas>
            </>
          )}
        </>
      )}
    </div>
  )
}

export default App
