import React, { Suspense, useState } from 'react'
import { Canvas } from '@react-three/fiber'
import { OrbitControls, Text } from '@react-three/drei'
import { XR, VRButton, ARButton } from '@react-three/xr'
import './App.css'

// POI数据
const POI_DATA = [
  {
    id: 'main-pit',
    name: '主矿坑',
    position: [0, 0, 0],
    color: '#ff6b6b',
    description: '汤山矿坑公园的核心景观，深达数十米的废弃采石坑，现已改造为生态景观湖。',
    details: '深度约40米，面积约15公顷，开采时间1950-2010年'
  },
  {
    id: 'observation-deck',
    name: '观景台',
    position: [8, 2, -5],
    color: '#4ecdc4',
    description: '最佳观赏矿坑全景的位置，可以俯瞰整个矿坑湖和周围的生态恢复区域。',
    details: '高度海拔120米，360度全景视野，最佳观赏时间日出日落'
  },
  {
    id: 'eco-trail',
    name: '生态步道',
    position: [-6, 0, 3],
    color: '#45b7d1',
    description: '环绕矿坑的生态步道，展示了从工业废地到生态公园的转变过程。',
    details: '总长度2.5公里，步行时间约45分钟，8个观景点'
  }
]

// POI标记组件
function POIMarker({ poi, onClick, isSelected }) {
  return (
    <mesh
      position={poi.position}
      onClick={() => onClick(poi)}
      onPointerOver={(e) => {
        e.object.material.emissive.setHex(0x444444)
        document.body.style.cursor = 'pointer'
      }}
      onPointerOut={(e) => {
        e.object.material.emissive.setHex(isSelected ? 0x222222 : 0x000000)
        document.body.style.cursor = 'default'
      }}
    >
      <sphereGeometry args={[isSelected ? 0.4 : 0.3, 16, 16]} />
      <meshStandardMaterial
        color={poi.color}
        emissive={isSelected ? poi.color : '#000000'}
        emissiveIntensity={isSelected ? 0.3 : 0}
      />
    </mesh>
  )
}

// 信息面板组件
function InfoPanel({ poi, onClose }) {
  if (!poi) return null

  return (
    <div style={{
      position: 'fixed',
      top: '20px',
      right: '20px',
      width: '300px',
      background: 'rgba(255, 255, 255, 0.95)',
      backdropFilter: 'blur(10px)',
      borderRadius: '15px',
      padding: '20px',
      boxShadow: '0 10px 30px rgba(0,0,0,0.3)',
      zIndex: 1000,
      color: '#333'
    }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
        <h3 style={{ margin: 0, color: poi.color, fontSize: '1.2rem' }}>{poi.name}</h3>
        <button
          onClick={onClose}
          style={{
            background: 'none',
            border: 'none',
            fontSize: '20px',
            cursor: 'pointer',
            color: '#666'
          }}
        >
          ✕
        </button>
      </div>
      <p style={{ margin: '0 0 10px 0', lineHeight: '1.5', fontSize: '14px' }}>
        {poi.description}
      </p>
      <div style={{
        background: 'rgba(78, 205, 196, 0.1)',
        padding: '10px',
        borderRadius: '8px',
        fontSize: '13px',
        color: '#555'
      }}>
        <strong>详细信息：</strong><br />
        {poi.details}
      </div>
    </div>
  )
}

// 3D场景组件
function BasicScene({ selectedPOI, onPOIClick }) {
  return (
    <>
      {/* 基础光照 */}
      <ambientLight intensity={0.5} />
      <directionalLight position={[10, 10, 5]} intensity={1} />

      {/* 地面 */}
      <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -2, 0]}>
        <planeGeometry args={[20, 20]} />
        <meshStandardMaterial color="#8B4513" />
      </mesh>

      {/* 矿坑水面 */}
      <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -1.8, 0]}>
        <circleGeometry args={[6, 32]} />
        <meshStandardMaterial color="#006994" transparent opacity={0.8} />
      </mesh>

      {/* POI标记 */}
      {POI_DATA.map((poi) => (
        <POIMarker
          key={poi.id}
          poi={poi}
          onClick={onPOIClick}
          isSelected={selectedPOI?.id === poi.id}
        />
      ))}

      {/* 场景标题 */}
      <Text
        position={[0, 8, -10]}
        fontSize={1}
        color="#ffffff"
        anchorX="center"
        anchorY="middle"
      >
        汤山矿坑公园 AR 体验
      </Text>

      {/* 相机控制 */}
      <OrbitControls
        enablePan={true}
        enableZoom={true}
        enableRotate={true}
        maxPolarAngle={Math.PI / 2}
      />
    </>
  )
}

function App() {
  const [showTest, setShowTest] = useState(true)
  const [selectedPOI, setSelectedPOI] = useState(null)

  const handlePOIClick = (poi) => {
    setSelectedPOI(selectedPOI?.id === poi.id ? null : poi)
  }
  return (
    <div className="app">
      {/* 切换按钮 */}
      <button
        onClick={() => setShowTest(!showTest)}
        style={{
          position: 'fixed',
          top: '20px',
          left: '20px',
          zIndex: 1000,
          padding: '10px 20px',
          background: 'rgba(78, 205, 196, 0.9)',
          color: 'white',
          border: 'none',
          borderRadius: '25px',
          cursor: 'pointer',
          fontSize: '14px',
          fontWeight: 'bold'
        }}
      >
        {showTest ? '🎮 启用3D场景' : '📋 返回测试页面'}
      </button>

      {showTest ? (
        // 测试页面
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          color: 'white',
          fontSize: '2rem',
          textAlign: 'center',
          background: 'rgba(0,0,0,0.7)',
          padding: '20px',
          borderRadius: '10px',
          zIndex: 1000
        }}>
          🏔️ 汤山矿坑公园 AR 互动平台 🏔️
          <br />
          <span style={{ fontSize: '1rem', marginTop: '10px', display: 'block' }}>
            ✅ 测试版本 - 基础功能正常
          </span>
          <div style={{
            fontSize: '0.8rem',
            marginTop: '15px',
            color: '#4ecdc4',
            display: 'flex',
            flexDirection: 'column',
            gap: '5px'
          }}>
            <div>🎮 React 应用正常运行</div>
            <div>🌐 开发服务器连接正常</div>
            <div>📱 移动端兼容测试通过</div>
            <div style={{ marginTop: '10px', color: '#feca57' }}>
              点击左上角按钮体验3D场景 →
            </div>
          </div>
        </div>
      ) : (
        // 3D场景
        <>
          {/* AR/VR 按钮 */}
          <div style={{
            position: 'fixed',
            top: '20px',
            right: selectedPOI ? '340px' : '20px',
            zIndex: 1000,
            display: 'flex',
            gap: '10px',
            transition: 'right 0.3s ease'
          }}>
            <ARButton
              style={{
                padding: '12px 20px',
                background: 'rgba(255, 255, 255, 0.9)',
                border: 'none',
                borderRadius: '25px',
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#333',
                cursor: 'pointer',
                backdropFilter: 'blur(10px)',
                boxShadow: '0 4px 15px rgba(0, 0, 0, 0.1)'
              }}
              sessionInit={{
                requiredFeatures: ['hit-test'],
                optionalFeatures: ['dom-overlay'],
                domOverlay: { root: document.body }
              }}
            />
            <VRButton
              style={{
                padding: '12px 20px',
                background: 'rgba(255, 255, 255, 0.9)',
                border: 'none',
                borderRadius: '25px',
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#333',
                cursor: 'pointer',
                backdropFilter: 'blur(10px)',
                boxShadow: '0 4px 15px rgba(0, 0, 0, 0.1)'
              }}
            />
          </div>

          {/* 功能指示器 */}
          <div style={{
            position: 'fixed',
            bottom: '20px',
            left: '20px',
            background: 'rgba(78, 205, 196, 0.9)',
            backdropFilter: 'blur(10px)',
            padding: '10px 15px',
            borderRadius: '20px',
            color: 'white',
            fontSize: '14px',
            fontWeight: '500',
            zIndex: 1000,
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}>
            <div style={{
              width: '8px',
              height: '8px',
              background: '#ffffff',
              borderRadius: '50%',
              animation: 'pulse 2s infinite'
            }}></div>
            AR 就绪
          </div>

          {/* 操作提示 */}
          <div style={{
            position: 'fixed',
            bottom: '20px',
            right: '20px',
            background: 'rgba(0, 0, 0, 0.7)',
            backdropFilter: 'blur(10px)',
            padding: '15px',
            borderRadius: '15px',
            color: 'white',
            fontSize: '12px',
            zIndex: 1000,
            maxWidth: '200px'
          }}>
            <div style={{ marginBottom: '8px', fontWeight: 'bold' }}>🎮 操作指南</div>
            <div>• 拖拽：旋转视角</div>
            <div>• 滚轮：缩放场景</div>
            <div>• 点击球体：查看详情</div>
            <div>• AR按钮：启用增强现实</div>
          </div>

          {/* 信息面板 */}
          <InfoPanel
            poi={selectedPOI}
            onClose={() => setSelectedPOI(null)}
          />

          <Canvas
            camera={{ position: [0, 5, 10], fov: 60 }}
            style={{ height: '100vh', width: '100vw' }}
          >
            <XR>
              <Suspense fallback={null}>
                <BasicScene
                  selectedPOI={selectedPOI}
                  onPOIClick={handlePOIClick}
                />
              </Suspense>
            </XR>
          </Canvas>
        </>
      )}
    </div>
  )
}

export default App
