import React from 'react'
import './App.css'

function App() {
  return (
    <div className="app">
      <h1 style={{
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        color: 'white',
        fontSize: '2rem',
        textAlign: 'center',
        background: 'rgba(0,0,0,0.7)',
        padding: '20px',
        borderRadius: '10px'
      }}>
        🏔️ 汤山矿坑公园 AR 互动平台 🏔️
        <br />
        <span style={{ fontSize: '1rem', marginTop: '10px', display: 'block' }}>
          测试版本 - 基础功能正常
        </span>
      </h1>
    </div>
  )
}

export default App

// 简化版本 - 测试完成后可以恢复完整功能
      name: '生态步道',
      position: [-6, 0, 3],
      description: '环绕矿坑的生态步道，展示了从工业废地到生态公园的转变过程。',
      type: 'trail'
    }
  ],
  history: [
    {
      id: 'mining-era',
      name: '采石年代',
      position: [-3, 0, -3],
      description: '1950-2010年的采石作业场景重现',
      type: 'historical'
    }
  ],
  nature: [
    {
      id: 'wetland',
      name: '湿地生态区',
      position: [-4, -1, 0],
      description: '矿坑底部形成的天然湿地，成为多种鸟类的栖息地',
      type: 'ecosystem'
    }
  ]
}

// POI标记组件
function POIMarker({ poi, onClick }) {
  const getMarkerColor = (type) => {
    switch (type) {
      case 'landmark': return '#ff6b6b'
      case 'viewpoint': return '#4ecdc4'
      case 'trail': return '#45b7d1'
      case 'historical': return '#feca57'
      case 'ecosystem': return '#0abde3'
      default: return '#ffffff'
    }
  }

  return (
    <mesh
      position={poi.position}
      onClick={() => onClick(poi)}
      onPointerOver={(e) => {
        e.object.material.emissive.setHex(0x444444)
        document.body.style.cursor = 'pointer'
      }}
      onPointerOut={(e) => {
        e.object.material.emissive.setHex(0x000000)
        document.body.style.cursor = 'default'
      }}
    >
      <sphereGeometry args={[0.3, 16, 16]} />
      <meshStandardMaterial color={getMarkerColor(poi.type)} />
    </mesh>
  )
}

// 简化版汤山场景
function TangshanSceneSimple({ scene, onPOIClick }) {
  const currentPOIs = POI_DATA[scene] || POI_DATA.overview
  return (
    <>
      {/* 基础光照 */}
      <ambientLight intensity={0.5} />
      <directionalLight position={[10, 10, 5]} intensity={1} />

      {/* 地面 */}
      <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -2, 0]}>
        <planeGeometry args={[20, 20]} />
        <meshStandardMaterial color="#8B4513" />
      </mesh>

      {/* 矿坑水面 */}
      <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -1.8, 0]}>
        <circleGeometry args={[6, 32]} />
        <meshStandardMaterial color="#006994" transparent opacity={0.8} />
      </mesh>

      {/* POI标记 */}
      {currentPOIs.map((poi) => (
        <POIMarker
          key={poi.id}
          poi={poi}
          onClick={onPOIClick}
        />
      ))}

      {/* 场景标题 */}
      <Text
        position={[0, 8, -10]}
        fontSize={1}
        color="#ffffff"
        anchorX="center"
        anchorY="middle"
      >
        汤山矿坑公园 AR 体验
      </Text>

      {/* 场景描述 */}
      <Text
        position={[0, 6.5, -10]}
        fontSize={0.4}
        color="#cccccc"
        anchorX="center"
        anchorY="middle"
        maxWidth={15}
      >
        {scene === 'overview' && '探索这个从废弃采石场转变为生态公园的奇迹'}
        {scene === 'history' && '回顾汤山采石的历史变迁'}
        {scene === 'nature' && '发现生态修复后的自然奇观'}
      </Text>

      {/* 相机控制 */}
      <OrbitControls
        enablePan={true}
        enableZoom={true}
        enableRotate={true}
        maxPolarAngle={Math.PI / 2}
      />
    </>
  )
}

function SimpleApp() {
  const [currentScene, setCurrentScene] = useState('overview')
  const [showInfo, setShowInfo] = useState(false)
  const [selectedPOI, setSelectedPOI] = useState(null)

  const handlePOIClick = (poi) => {
    setSelectedPOI(poi)
    setShowInfo(true)
  }

  return (
    <div className="app">
      {/* AR/VR 按钮 */}
      <div className="xr-buttons">
        <ARButton
          sessionInit={{
            requiredFeatures: ['hit-test'],
            optionalFeatures: ['dom-overlay'],
            domOverlay: { root: document.body }
          }}
        />
        <VRButton />
      </div>

      {/* 导航菜单 */}
      <Navigation
        currentScene={currentScene}
        onSceneChange={setCurrentScene}
      />

      {/* 信息面板 */}
      {showInfo && selectedPOI && (
        <InfoPanel
          poi={selectedPOI}
          onClose={() => setShowInfo(false)}
        />
      )}

      <Canvas
        camera={{ position: [0, 5, 10], fov: 60 }}
        style={{ height: '100vh', width: '100vw' }}
      >
        <XR>
          <Suspense fallback={null}>
            <TangshanSceneSimple
              scene={currentScene}
              onPOIClick={handlePOIClick}
            />

            {/* VR/AR 控制器 */}
            <Controllers />
            <Hands />
          </Suspense>
        </XR>
      </Canvas>
    </div>
  )
}

function App() {
  return <SimpleApp />
}

export default App
