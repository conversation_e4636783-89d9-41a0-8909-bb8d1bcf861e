.info-panel {
  position: fixed;
  top: 0;
  right: 0;
  width: 400px;
  height: 100vh;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-left: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 1000;
  overflow-y: auto;
  transform: translateX(100%);
  animation: slideIn 0.3s ease-out forwards;
  box-shadow: -5px 0 25px rgba(0, 0, 0, 0.1);
}

@keyframes slideIn {
  to {
    transform: translateX(0);
  }
}

.info-panel-content {
  padding: 20px;
  height: 100%;
}

/* 头部样式 */
.info-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e0e0e0;
}

.info-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.type-icon {
  font-size: 24px;
}

.info-title h2 {
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: bold;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  color: #666;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.2s ease;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-button:hover {
  background: rgba(0, 0, 0, 0.1);
  color: #333;
}

/* 描述样式 */
.info-description {
  margin-bottom: 25px;
}

.info-description p {
  color: #555;
  line-height: 1.6;
  font-size: 16px;
  margin: 0;
}

/* 信息区块样式 */
.info-section {
  margin-bottom: 25px;
}

.info-section h3 {
  color: #333;
  font-size: 18px;
  margin-bottom: 12px;
  font-weight: bold;
  border-left: 4px solid #4ecdc4;
  padding-left: 12px;
}

.info-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.info-list li {
  padding: 8px 0;
  color: #666;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
  padding-left: 20px;
}

.info-list li:before {
  content: "•";
  color: #4ecdc4;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.info-list li:last-child {
  border-bottom: none;
}

/* 时间线样式 */
.timeline {
  position: relative;
  padding-left: 20px;
}

.timeline:before {
  content: '';
  position: absolute;
  left: 8px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, #4ecdc4, #45b7d1);
}

.timeline-item {
  position: relative;
  margin-bottom: 20px;
  padding-left: 25px;
}

.timeline-dot {
  position: absolute;
  left: -8px;
  top: 5px;
  width: 12px;
  height: 12px;
  background: #4ecdc4;
  border-radius: 50%;
  border: 3px solid white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.timeline-content {
  color: #666;
  line-height: 1.5;
  font-size: 14px;
}

/* 操作按钮样式 */
.info-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin: 25px 0;
  padding: 20px 0;
  border-top: 1px solid #e0e0e0;
  border-bottom: 1px solid #e0e0e0;
}

.action-button {
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.action-button.primary {
  background: linear-gradient(135deg, #4ecdc4, #45b7d1);
  color: white;
}

.action-button.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(78, 205, 196, 0.3);
}

.action-button.secondary {
  background: rgba(78, 205, 196, 0.1);
  color: #4ecdc4;
  border: 1px solid rgba(78, 205, 196, 0.3);
}

.action-button.secondary:hover {
  background: rgba(78, 205, 196, 0.2);
  border-color: #4ecdc4;
}

/* 推荐区域样式 */
.recommendations {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.recommendation-item {
  padding: 10px 12px;
  background: rgba(69, 183, 209, 0.1);
  border-radius: 6px;
  color: #45b7d1;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 3px solid #45b7d1;
}

.recommendation-item:hover {
  background: rgba(69, 183, 209, 0.2);
  transform: translateX(5px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-panel {
    width: 100vw;
    transform: translateY(100%);
    animation: slideUp 0.3s ease-out forwards;
  }
  
  @keyframes slideUp {
    to {
      transform: translateY(0);
    }
  }
  
  .info-panel-content {
    padding: 15px;
  }
  
  .info-title h2 {
    font-size: 20px;
  }
  
  .info-description p {
    font-size: 14px;
  }
  
  .info-section h3 {
    font-size: 16px;
  }
  
  .action-button {
    padding: 10px 14px;
    font-size: 13px;
  }
}

/* 滚动条样式 */
.info-panel::-webkit-scrollbar {
  width: 6px;
}

.info-panel::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

.info-panel::-webkit-scrollbar-thumb {
  background: rgba(78, 205, 196, 0.5);
  border-radius: 3px;
}

.info-panel::-webkit-scrollbar-thumb:hover {
  background: rgba(78, 205, 196, 0.7);
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  .info-panel {
    animation: none;
    transform: translateX(0);
  }
  
  .action-button:hover {
    transform: none;
  }
  
  .recommendation-item:hover {
    transform: none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .info-panel {
    background: #ffffff;
    border-left: 2px solid #000000;
  }
  
  .info-title h2 {
    color: #000000;
  }
  
  .info-description p {
    color: #000000;
  }
  
  .action-button.primary {
    background: #000000;
    color: #ffffff;
  }
  
  .action-button.secondary {
    background: #ffffff;
    color: #000000;
    border: 2px solid #000000;
  }
}
