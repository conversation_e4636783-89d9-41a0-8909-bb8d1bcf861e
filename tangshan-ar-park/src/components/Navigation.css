.navigation {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}

/* 菜单切换按钮 */
.menu-toggle {
  position: fixed;
  top: 20px;
  left: 20px;
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 4px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  z-index: 1001;
}

.menu-toggle:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.05);
}

.menu-toggle span {
  width: 20px;
  height: 2px;
  background: #333;
  transition: all 0.3s ease;
  border-radius: 1px;
}

.menu-toggle.active span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.menu-toggle.active span:nth-child(2) {
  opacity: 0;
}

.menu-toggle.active span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* 导航菜单 */
.nav-menu {
  position: fixed;
  top: 0;
  left: 0;
  width: 350px;
  height: 100vh;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  overflow-y: auto;
  box-shadow: 5px 0 25px rgba(0, 0, 0, 0.1);
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}

.nav-menu.open {
  transform: translateX(0);
}

.nav-menu::-webkit-scrollbar {
  width: 6px;
}

.nav-menu::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

.nav-menu::-webkit-scrollbar-thumb {
  background: rgba(78, 205, 196, 0.5);
  border-radius: 3px;
}

/* 导航头部 */
.nav-header {
  padding: 80px 20px 20px;
  background: linear-gradient(135deg, #4ecdc4, #45b7d1);
  color: white;
  text-align: center;
}

.nav-header h2 {
  margin: 0 0 5px 0;
  font-size: 24px;
  font-weight: bold;
}

.nav-header p {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
}

/* 导航区块 */
.nav-sections {
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
}

.nav-sections:last-child {
  border-bottom: none;
}

.nav-sections h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #333;
  font-weight: bold;
}

/* 场景列表 */
.scene-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.scene-button {
  width: 100%;
  padding: 15px;
  background: none;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 15px;
  text-align: left;
  margin-bottom: 10px;
}

.scene-button:hover {
  background: rgba(78, 205, 196, 0.1);
}

.scene-button.active {
  background: linear-gradient(135deg, #4ecdc4, #45b7d1);
  color: white;
}

.scene-icon {
  font-size: 24px;
  min-width: 30px;
}

.scene-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.scene-name {
  font-size: 16px;
  font-weight: bold;
}

.scene-description {
  font-size: 12px;
  opacity: 0.8;
}

/* 功能列表 */
.function-list,
.quick-nav {
  list-style: none;
  padding: 0;
  margin: 0;
}

.function-button,
.quick-nav-button {
  width: 100%;
  padding: 12px 15px;
  background: none;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 12px;
  text-align: left;
  margin-bottom: 5px;
  color: #666;
}

.function-button:hover,
.quick-nav-button:hover {
  background: rgba(69, 183, 209, 0.1);
  color: #45b7d1;
}

.function-icon,
.quick-icon {
  font-size: 18px;
  min-width: 20px;
}

/* 导航底部 */
.nav-footer {
  padding: 20px;
  background: rgba(0, 0, 0, 0.05);
}

.park-info,
.weather-info {
  margin-bottom: 20px;
}

.park-info h4,
.weather-info h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #333;
  font-weight: bold;
}

.park-info p {
  margin: 5px 0;
  font-size: 12px;
  color: #666;
}

.weather-display {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 5px;
}

.weather-icon {
  font-size: 24px;
}

.weather-details {
  display: flex;
  flex-direction: column;
}

.temperature {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.weather-desc {
  font-size: 12px;
  color: #666;
}

.weather-tip {
  margin: 5px 0 0 0;
  font-size: 11px;
  color: #4ecdc4;
  font-style: italic;
}

/* 遮罩层 */
.nav-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.3);
  z-index: 999;
}

/* 当前场景指示器 */
.current-scene-indicator {
  position: fixed;
  bottom: 100px;
  left: 20px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  padding: 10px 15px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  z-index: 500;
}

.current-scene-indicator .scene-icon {
  font-size: 18px;
}

.current-scene-indicator .scene-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

/* AR状态指示器 */
.ar-status {
  position: fixed;
  bottom: 40px;
  left: 20px;
  z-index: 500;
}

.ar-indicator {
  background: rgba(78, 205, 196, 0.9);
  backdrop-filter: blur(10px);
  padding: 8px 12px;
  border-radius: 15px;
  display: flex;
  align-items: center;
  gap: 6px;
  box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
}

.ar-dot {
  width: 8px;
  height: 8px;
  background: #ffffff;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.ar-text {
  font-size: 12px;
  color: white;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-menu {
    width: 100vw;
  }
  
  .menu-toggle {
    top: 15px;
    left: 15px;
    width: 45px;
    height: 45px;
  }
  
  .nav-header {
    padding: 70px 15px 15px;
  }
  
  .nav-sections {
    padding: 15px;
  }
  
  .current-scene-indicator {
    bottom: 80px;
    left: 15px;
    padding: 8px 12px;
  }
  
  .ar-status {
    bottom: 30px;
    left: 15px;
  }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  .nav-menu {
    transition: none;
  }
  
  .menu-toggle span {
    transition: none;
  }
  
  .ar-dot {
    animation: none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .nav-menu {
    background: #ffffff;
    border-right: 2px solid #000000;
  }
  
  .menu-toggle {
    background: #ffffff;
    border: 2px solid #000000;
  }
  
  .scene-button.active {
    background: #000000;
    color: #ffffff;
  }
}
