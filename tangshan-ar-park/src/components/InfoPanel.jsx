import React from 'react'
import './InfoPanel.css'

function InfoPanel({ poi, onClose }) {
  if (!poi) return null

  const getTypeIcon = (type) => {
    switch (type) {
      case 'landmark': return '🏔️'
      case 'viewpoint': return '👁️'
      case 'trail': return '🚶'
      case 'building': return '🏛️'
      case 'historical': return '📜'
      case 'restoration': return '🌱'
      case 'ecosystem': return '🦆'
      case 'garden': return '🌸'
      default: return '📍'
    }
  }

  const getDetailedInfo = (poi) => {
    const details = {
      'main-pit': {
        facts: [
          '深度：约40米',
          '面积：约15公顷',
          '开采时间：1950-2010年',
          '年产量：曾达100万吨'
        ],
        timeline: [
          '1950年：开始采石作业',
          '1980年：达到开采高峰期',
          '2010年：停止开采',
          '2015年：开始生态修复',
          '2018年：正式开放为公园'
        ]
      },
      'observation-deck': {
        facts: [
          '高度：海拔120米',
          '视野：360度全景',
          '最佳观赏时间：日出日落',
          '可见范围：半径5公里'
        ],
        features: [
          '矿坑全景观赏',
          '远山景色',
          '城市天际线',
          '生态恢复区域'
        ]
      },
      'eco-trail': {
        facts: [
          '总长度：2.5公里',
          '步行时间：约45分钟',
          '高度变化：30米',
          '观景点：8个'
        ],
        highlights: [
          '植被恢复展示区',
          '地质剖面观察点',
          '鸟类观测站',
          '历史遗迹保护区'
        ]
      },
      'museum': {
        facts: [
          '建筑面积：2000平方米',
          '展品数量：500余件',
          '开放时间：9:00-17:00',
          '年接待量：10万人次'
        ],
        exhibitions: [
          '汤山地质历史',
          '采石工业发展',
          '生态修复技术',
          '未来规划展望'
        ]
      }
    }
    return details[poi.id] || {}
  }

  const detailInfo = getDetailedInfo(poi)

  return (
    <div className="info-panel">
      <div className="info-panel-content">
        {/* 头部 */}
        <div className="info-header">
          <div className="info-title">
            <span className="type-icon">{getTypeIcon(poi.type)}</span>
            <h2>{poi.name}</h2>
          </div>
          <button className="close-button" onClick={onClose}>
            ✕
          </button>
        </div>

        {/* 主要描述 */}
        <div className="info-description">
          <p>{poi.description}</p>
        </div>

        {/* 历史信息 */}
        {poi.history && (
          <div className="info-section">
            <h3>历史背景</h3>
            <p>{poi.history}</p>
          </div>
        )}

        {/* 详细信息 */}
        {detailInfo.facts && (
          <div className="info-section">
            <h3>基本信息</h3>
            <ul className="info-list">
              {detailInfo.facts.map((fact, index) => (
                <li key={index}>{fact}</li>
              ))}
            </ul>
          </div>
        )}

        {/* 时间线 */}
        {detailInfo.timeline && (
          <div className="info-section">
            <h3>发展历程</h3>
            <div className="timeline">
              {detailInfo.timeline.map((event, index) => (
                <div key={index} className="timeline-item">
                  <div className="timeline-dot"></div>
                  <div className="timeline-content">{event}</div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 特色亮点 */}
        {detailInfo.features && (
          <div className="info-section">
            <h3>观赏亮点</h3>
            <ul className="info-list">
              {detailInfo.features.map((feature, index) => (
                <li key={index}>{feature}</li>
              ))}
            </ul>
          </div>
        )}

        {/* 步道亮点 */}
        {detailInfo.highlights && (
          <div className="info-section">
            <h3>步道亮点</h3>
            <ul className="info-list">
              {detailInfo.highlights.map((highlight, index) => (
                <li key={index}>{highlight}</li>
              ))}
            </ul>
          </div>
        )}

        {/* 展览内容 */}
        {detailInfo.exhibitions && (
          <div className="info-section">
            <h3>展览内容</h3>
            <ul className="info-list">
              {detailInfo.exhibitions.map((exhibition, index) => (
                <li key={index}>{exhibition}</li>
              ))}
            </ul>
          </div>
        )}

        {/* 互动按钮 */}
        <div className="info-actions">
          <button className="action-button primary">
            📍 导航到此
          </button>
          <button className="action-button secondary">
            📷 拍照留念
          </button>
          <button className="action-button secondary">
            🔊 语音讲解
          </button>
        </div>

        {/* 相关推荐 */}
        <div className="info-section">
          <h3>相关推荐</h3>
          <div className="recommendations">
            {poi.type === 'landmark' && (
              <>
                <div className="recommendation-item">观景台 - 最佳观赏角度</div>
                <div className="recommendation-item">生态步道 - 深度探索</div>
              </>
            )}
            {poi.type === 'viewpoint' && (
              <>
                <div className="recommendation-item">主矿坑 - 核心景观</div>
                <div className="recommendation-item">矿业博物馆 - 了解历史</div>
              </>
            )}
            {poi.type === 'trail' && (
              <>
                <div className="recommendation-item">湿地生态区 - 生物多样性</div>
                <div className="recommendation-item">岩石花园 - 垂直景观</div>
              </>
            )}
            {poi.type === 'building' && (
              <>
                <div className="recommendation-item">主矿坑 - 实地体验</div>
                <div className="recommendation-item">观景台 - 全景视野</div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default InfoPanel
