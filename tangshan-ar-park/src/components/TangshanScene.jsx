import React, { useRef, useState, useEffect } from 'react'
import { useFrame } from '@react-three/fiber'
import { Text, Box, Sphere, Cylinder, Plane, Html } from '@react-three/drei'
import * as THREE from 'three'

// 汤山矿坑公园的兴趣点数据
const POI_DATA = {
  overview: [
    {
      id: 'main-pit',
      name: '主矿坑',
      position: [0, -5, 0],
      description: '汤山矿坑公园的核心景观，深达数十米的废弃采石坑，现已改造为生态景观湖。',
      type: 'landmark',
      history: '始建于1950年代，是南京重要的建材采石场，2010年停止开采后改造为公园。'
    },
    {
      id: 'observation-deck',
      name: '观景台',
      position: [8, 2, -5],
      description: '最佳观赏矿坑全景的位置，可以俯瞰整个矿坑湖和周围的生态恢复区域。',
      type: 'viewpoint'
    },
    {
      id: 'eco-trail',
      name: '生态步道',
      position: [-6, 0, 3],
      description: '环绕矿坑的生态步道，展示了从工业废地到生态公园的转变过程。',
      type: 'trail'
    },
    {
      id: 'museum',
      name: '矿业博物馆',
      position: [5, 1, 8],
      description: '展示汤山地区的采石历史和地质文化，以及生态修复的成果。',
      type: 'building'
    }
  ],
  history: [
    {
      id: 'mining-era',
      name: '采石年代',
      position: [-3, 0, -3],
      description: '1950-2010年的采石作业场景重现',
      type: 'historical'
    },
    {
      id: 'restoration',
      name: '生态修复',
      position: [3, 0, 3],
      description: '2010年后的生态修复过程展示',
      type: 'restoration'
    }
  ],
  nature: [
    {
      id: 'wetland',
      name: '湿地生态区',
      position: [-4, -2, 0],
      description: '矿坑底部形成的天然湿地，成为多种鸟类的栖息地',
      type: 'ecosystem'
    },
    {
      id: 'rock-garden',
      name: '岩石花园',
      position: [2, 1, -4],
      description: '利用采石留下的岩壁打造的垂直花园',
      type: 'garden'
    }
  ]
}

// POI标记组件
function POIMarker({ poi, onClick }) {
  const meshRef = useRef()
  const [hovered, setHovered] = useState(false)

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y = state.clock.elapsedTime
      meshRef.current.position.y = poi.position[1] + Math.sin(state.clock.elapsedTime * 2) * 0.2
    }
  })

  const getMarkerColor = (type) => {
    switch (type) {
      case 'landmark': return '#ff6b6b'
      case 'viewpoint': return '#4ecdc4'
      case 'trail': return '#45b7d1'
      case 'building': return '#96ceb4'
      case 'historical': return '#feca57'
      case 'restoration': return '#48dbfb'
      case 'ecosystem': return '#0abde3'
      case 'garden': return '#00d2d3'
      default: return '#ffffff'
    }
  }

  return (
    <group position={poi.position}>
      {/* 主标记球体 */}
      <Sphere
        ref={meshRef}
        args={[0.3, 16, 16]}
        position={[0, 1, 0]}
        onClick={() => onClick(poi)}
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
      >
        <meshStandardMaterial 
          color={getMarkerColor(poi.type)}
          emissive={hovered ? getMarkerColor(poi.type) : '#000000'}
          emissiveIntensity={hovered ? 0.3 : 0}
        />
      </Sphere>

      {/* 标记柱子 */}
      <Cylinder args={[0.05, 0.05, 1]} position={[0, 0.5, 0]}>
        <meshStandardMaterial color="#666666" />
      </Cylinder>

      {/* 文字标签 */}
      <Text
        position={[0, 2, 0]}
        fontSize={0.3}
        color="#ffffff"
        anchorX="center"
        anchorY="middle"
        outlineWidth={0.02}
        outlineColor="#000000"
      >
        {poi.name}
      </Text>

      {/* 悬停信息 */}
      {hovered && (
        <Html position={[0, 2.5, 0]} center>
          <div className="poi-tooltip">
            <h4>{poi.name}</h4>
            <p>{poi.description}</p>
          </div>
        </Html>
      )}
    </group>
  )
}

// 矿坑地形组件
function MinePitTerrain() {
  const terrainRef = useRef()

  useEffect(() => {
    if (terrainRef.current) {
      // 创建矿坑地形的几何体
      const geometry = terrainRef.current.geometry
      const positions = geometry.attributes.position.array

      // 修改顶点位置创建矿坑形状
      for (let i = 0; i < positions.length; i += 3) {
        const x = positions[i]
        const z = positions[i + 2]
        const distance = Math.sqrt(x * x + z * z)
        
        // 创建碗状地形
        if (distance < 8) {
          positions[i + 1] = -Math.pow(8 - distance, 2) * 0.1 - 2
        }
      }
      
      geometry.attributes.position.needsUpdate = true
      geometry.computeVertexNormals()
    }
  }, [])

  return (
    <Plane
      ref={terrainRef}
      args={[20, 20, 50, 50]}
      rotation={[-Math.PI / 2, 0, 0]}
      position={[0, -3, 0]}
    >
      <meshStandardMaterial 
        color="#8B4513"
        wireframe={false}
        roughness={0.8}
      />
    </Plane>
  )
}

// 水面组件
function WaterSurface() {
  const waterRef = useRef()

  useFrame((state) => {
    if (waterRef.current) {
      waterRef.current.material.uniforms.time.value = state.clock.elapsedTime
    }
  })

  const waterMaterial = new THREE.ShaderMaterial({
    uniforms: {
      time: { value: 0 },
      color: { value: new THREE.Color('#006994') }
    },
    vertexShader: `
      uniform float time;
      varying vec2 vUv;
      
      void main() {
        vUv = uv;
        vec3 pos = position;
        pos.z += sin(pos.x * 2.0 + time) * 0.1;
        pos.z += sin(pos.y * 2.0 + time * 0.8) * 0.1;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);
      }
    `,
    fragmentShader: `
      uniform vec3 color;
      varying vec2 vUv;
      
      void main() {
        gl_FragColor = vec4(color, 0.8);
      }
    `,
    transparent: true
  })

  return (
    <Plane
      ref={waterRef}
      args={[12, 12]}
      rotation={[-Math.PI / 2, 0, 0]}
      position={[0, -2.5, 0]}
      material={waterMaterial}
    />
  )
}

// 主场景组件
function TangshanScene({ scene, onPOIClick }) {
  const currentPOIs = POI_DATA[scene] || POI_DATA.overview

  return (
    <group>
      {/* 地形 */}
      <MinePitTerrain />
      
      {/* 水面 */}
      <WaterSurface />

      {/* 兴趣点标记 */}
      {currentPOIs.map((poi) => (
        <POIMarker
          key={poi.id}
          poi={poi}
          onClick={onPOIClick}
        />
      ))}

      {/* 场景标题 */}
      <Text
        position={[0, 8, -10]}
        fontSize={1}
        color="#ffffff"
        anchorX="center"
        anchorY="middle"
        outlineWidth={0.05}
        outlineColor="#000000"
      >
        汤山矿坑公园 AR 体验
      </Text>

      {/* 场景描述 */}
      <Text
        position={[0, 6.5, -10]}
        fontSize={0.4}
        color="#cccccc"
        anchorX="center"
        anchorY="middle"
        outlineWidth={0.02}
        outlineColor="#000000"
        maxWidth={15}
      >
        {scene === 'overview' && '探索这个从废弃采石场转变为生态公园的奇迹'}
        {scene === 'history' && '回顾汤山采石的历史变迁'}
        {scene === 'nature' && '发现生态修复后的自然奇观'}
      </Text>
    </group>
  )
}

export default TangshanScene
