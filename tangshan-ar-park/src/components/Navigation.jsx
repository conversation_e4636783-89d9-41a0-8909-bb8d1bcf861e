import React, { useState } from 'react'
import './Navigation.css'

function Navigation({ currentScene, onSceneChange }) {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const scenes = [
    {
      id: 'overview',
      name: '全景概览',
      icon: '🏞️',
      description: '鸟瞰整个矿坑公园'
    },
    {
      id: 'history',
      name: '历史回顾',
      icon: '📜',
      description: '了解采石历史变迁'
    },
    {
      id: 'nature',
      name: '生态自然',
      icon: '🌿',
      description: '探索生态修复成果'
    }
  ]

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  const handleSceneChange = (sceneId) => {
    onSceneChange(sceneId)
    setIsMenuOpen(false)
  }

  return (
    <div className="navigation">
      {/* 菜单按钮 */}
      <button 
        className={`menu-toggle ${isMenuOpen ? 'active' : ''}`}
        onClick={toggleMenu}
        aria-label="切换菜单"
      >
        <span></span>
        <span></span>
        <span></span>
      </button>

      {/* 导航菜单 */}
      <nav className={`nav-menu ${isMenuOpen ? 'open' : ''}`}>
        <div className="nav-header">
          <h2>汤山矿坑公园</h2>
          <p>AR 互动体验</p>
        </div>

        <div className="nav-sections">
          <h3>场景选择</h3>
          <ul className="scene-list">
            {scenes.map((scene) => (
              <li key={scene.id}>
                <button
                  className={`scene-button ${currentScene === scene.id ? 'active' : ''}`}
                  onClick={() => handleSceneChange(scene.id)}
                >
                  <span className="scene-icon">{scene.icon}</span>
                  <div className="scene-info">
                    <span className="scene-name">{scene.name}</span>
                    <span className="scene-description">{scene.description}</span>
                  </div>
                </button>
              </li>
            ))}
          </ul>
        </div>

        <div className="nav-sections">
          <h3>功能选项</h3>
          <ul className="function-list">
            <li>
              <button className="function-button">
                <span className="function-icon">🗺️</span>
                <span>园区地图</span>
              </button>
            </li>
            <li>
              <button className="function-button">
                <span className="function-icon">📱</span>
                <span>AR 扫描</span>
              </button>
            </li>
            <li>
              <button className="function-button">
                <span className="function-icon">🎧</span>
                <span>语音导览</span>
              </button>
            </li>
            <li>
              <button className="function-button">
                <span className="function-icon">📷</span>
                <span>拍照分享</span>
              </button>
            </li>
            <li>
              <button className="function-button">
                <span className="function-icon">ℹ️</span>
                <span>使用帮助</span>
              </button>
            </li>
          </ul>
        </div>

        <div className="nav-sections">
          <h3>快速导航</h3>
          <ul className="quick-nav">
            <li>
              <button className="quick-nav-button">
                <span className="quick-icon">🏔️</span>
                <span>主矿坑</span>
              </button>
            </li>
            <li>
              <button className="quick-nav-button">
                <span className="quick-icon">👁️</span>
                <span>观景台</span>
              </button>
            </li>
            <li>
              <button className="quick-nav-button">
                <span className="quick-icon">🚶</span>
                <span>生态步道</span>
              </button>
            </li>
            <li>
              <button className="quick-nav-button">
                <span className="quick-icon">🏛️</span>
                <span>博物馆</span>
              </button>
            </li>
          </ul>
        </div>

        <div className="nav-footer">
          <div className="park-info">
            <h4>开放信息</h4>
            <p>开放时间：8:00 - 18:00</p>
            <p>地址：南京市江宁区汤山街道</p>
            <p>咨询电话：025-84181777</p>
          </div>
          
          <div className="weather-info">
            <h4>今日天气</h4>
            <div className="weather-display">
              <span className="weather-icon">☀️</span>
              <div className="weather-details">
                <span className="temperature">22°C</span>
                <span className="weather-desc">晴朗</span>
              </div>
            </div>
            <p className="weather-tip">适宜户外游览</p>
          </div>
        </div>
      </nav>

      {/* 遮罩层 */}
      {isMenuOpen && (
        <div 
          className="nav-overlay"
          onClick={() => setIsMenuOpen(false)}
        />
      )}

      {/* 当前场景指示器 */}
      <div className="current-scene-indicator">
        <span className="scene-icon">
          {scenes.find(s => s.id === currentScene)?.icon}
        </span>
        <span className="scene-name">
          {scenes.find(s => s.id === currentScene)?.name}
        </span>
      </div>

      {/* AR状态指示器 */}
      <div className="ar-status">
        <div className="ar-indicator">
          <span className="ar-dot"></span>
          <span className="ar-text">AR 就绪</span>
        </div>
      </div>
    </div>
  )
}

export default Navigation
