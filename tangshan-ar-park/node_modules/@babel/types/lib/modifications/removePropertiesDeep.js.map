{"version": 3, "names": ["_traverseFast", "require", "_removeProperties", "removePropertiesDeep", "tree", "opts", "traverseFast", "removeProperties"], "sources": ["../../src/modifications/removePropertiesDeep.ts"], "sourcesContent": ["import traverseFast from \"../traverse/traverseFast.ts\";\nimport removeProperties from \"./removeProperties.ts\";\nimport type * as t from \"../index.ts\";\n\nexport default function removePropertiesDeep<T extends t.Node>(\n  tree: T,\n  opts?: { preserveComments: boolean } | null,\n): T {\n  traverseFast(tree, removeProperties, opts);\n\n  return tree;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,aAAA,GAAAC,OAAA;AACA,IAAAC,iBAAA,GAAAD,OAAA;AAGe,SAASE,oBAAoBA,CAC1CC,IAAO,EACPC,IAA2C,EACxC;EACH,IAAAC,qBAAY,EAACF,IAAI,EAAEG,yBAAgB,EAAEF,IAAI,CAAC;EAE1C,OAAOD,IAAI;AACb", "ignoreList": []}