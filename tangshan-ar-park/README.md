# 汤山矿坑公园 AR 互动平台

一个专为汤山矿坑公园设计的手机版AR互动平台，让游客通过增强现实技术深度体验公园的历史文化和自然景观。

## 🌟 项目特色

### 🏞️ 沉浸式AR体验
- **三维场景重现**：使用Three.js技术重现汤山矿坑的真实地形
- **互动式POI标记**：点击兴趣点获取详细信息和历史背景
- **多场景切换**：全景概览、历史回顾、生态自然三大主题场景

### 📱 移动端优化
- **响应式设计**：完美适配各种移动设备屏幕
- **触摸友好**：优化的触摸交互体验
- **流畅动画**：60fps的3D渲染性能

### 🎯 核心功能

#### 1. 场景导览
- **全景概览**：鸟瞰整个矿坑公园布局
- **历史回顾**：重现1950-2010年的采石历史
- **生态自然**：展示生态修复后的自然奇观

#### 2. 兴趣点系统
- **主矿坑**：核心景观，深达数十米的废弃采石坑
- **观景台**：最佳观赏位置，360度全景视野
- **生态步道**：环绕矿坑的生态恢复展示路径
- **矿业博物馆**：展示采石历史和地质文化

#### 3. 互动功能
- **AR扫描**：支持WebXR的AR体验
- **语音导览**：专业的景点讲解
- **拍照分享**：记录美好的游览时光
- **导航指引**：基于位置的智能导航

## 🚀 技术架构

### 前端技术栈
- **React 18**：现代化的用户界面框架
- **Vite**：快速的开发构建工具
- **Three.js**：强大的3D图形渲染引擎
- **@react-three/fiber**：React的Three.js集成
- **@react-three/drei**：Three.js的实用工具库
- **@react-three/xr**：WebXR支持，实现AR/VR功能

### 核心组件
```
src/
├── App.jsx                 # 主应用组件
├── components/
│   ├── TangshanScene.jsx   # 汤山场景组件
│   ├── InfoPanel.jsx       # 信息面板组件
│   └── Navigation.jsx      # 导航组件
└── styles/
    ├── App.css            # 主样式
    ├── InfoPanel.css      # 信息面板样式
    └── Navigation.css     # 导航样式
```

## 📦 安装和运行

### 环境要求
- Node.js 20.10.0 或更高版本
- npm 或 yarn 包管理器
- 支持WebGL的现代浏览器

### 安装步骤

1. **安装依赖**
```bash
npm install
```

2. **启动开发服务器**
```bash
npm run dev
```

3. **访问应用**
打开浏览器访问 `http://localhost:5173`

### 构建生产版本
```bash
npm run build
```

## 🎮 使用指南

### 基础操作
1. **场景切换**：点击左上角菜单按钮，选择不同主题场景
2. **视角控制**：
   - 拖拽：旋转视角
   - 滚轮：缩放视图
   - 双指：移动端缩放
3. **POI交互**：点击场景中的彩色标记球体查看详细信息

### AR功能
1. **启用AR**：点击右上角的"AR"按钮
2. **扫描环境**：将手机摄像头对准平面
3. **放置场景**：点击检测到的平面放置3D模型

### 导航功能
- **当前场景指示器**：左下角显示当前场景
- **AR状态指示器**：显示AR功能状态
- **快速导航**：菜单中的快速导航按钮

## 🌍 景点介绍

### 主要景点

#### 🏔️ 主矿坑
- **深度**：约40米
- **面积**：约15公顷
- **历史**：1950-2010年开采历史
- **现状**：改造为生态景观湖

#### 👁️ 观景台
- **高度**：海拔120米
- **视野**：360度全景
- **特色**：俯瞰矿坑全景的最佳位置

#### 🚶 生态步道
- **长度**：2.5公里
- **用时**：约45分钟
- **亮点**：展示生态修复过程

#### 🏛️ 矿业博物馆
- **面积**：2000平方米
- **展品**：500余件历史文物
- **主题**：采石历史与生态修复

## 📱 兼容性

### 支持的浏览器
- Chrome 90+
- Safari 14+
- Firefox 88+
- Edge 90+

### 移动设备
- iOS 14+ (Safari)
- Android 8+ (Chrome)

### AR功能要求
- 支持WebXR的设备和浏览器
- 摄像头权限
- 陀螺仪传感器

---

**汤山矿坑公园AR互动平台** - 让科技与自然完美融合，为游客带来前所未有的沉浸式体验！
