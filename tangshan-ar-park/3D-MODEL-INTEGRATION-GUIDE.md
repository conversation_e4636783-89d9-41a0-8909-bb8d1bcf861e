# 🎨 汤山矿坑公园 3D模型集成指南

## 📁 文件结构

```
tangshan-ar-park/
├── public/
│   └── models/           # 3D模型文件夹
│       ├── obj/              # OBJ格式模型
│       │   ├── main-pit.obj      # 主矿坑OBJ模型
│       │   ├── main-pit.mtl      # 主矿坑材质文件
│       │   ├── eco-trail.obj     # 生态步道OBJ模型
│       │   └── eco-trail.mtl     # 生态步道材质文件
│       ├── glb/              # GLB格式模型
│       │   ├── observation-deck.glb  # 观景台GLB模型
│       │   └── other-models.glb
│       └── textures/         # 纹理文件夹（可选）
├── src/
│   └── App.jsx          # 主应用文件
└── README.md
```

## 🎯 支持的3D模型格式

### ✅ 完全支持的格式
- **GLB** (推荐) - 二进制GLTF，包含所有资源，最佳性能
- **GLTF** - JSON格式，支持外部资源
- **OBJ** (新增) - 广泛支持的格式，需要配合MTL材质文件

### ⚠️ 需要转换的格式
- **FBX** - 需要转换为GLB/OBJ
- **DAE** - 需要转换为GLB/OBJ
- **3DS** - 需要转换为GLB/OBJ

## 🔧 如何添加自己的3D模型

### 步骤1：准备模型文件

#### GLB格式（推荐）
1. 将您的3D模型导出为GLB格式
2. 将GLB文件放入 `public/models/glb/` 文件夹

#### OBJ格式（新增支持）
1. 导出OBJ文件和对应的MTL材质文件
2. 将OBJ和MTL文件放入 `public/models/obj/` 文件夹
3. 确保MTL文件与OBJ文件同名
4. 纹理图片放入 `public/models/textures/` 文件夹

### 步骤2：更新POI数据
在 `src/App.jsx` 中找到 `POI_DATA` 数组，更新模型信息：

```javascript
const POI_DATA = [
  {
    id: 'your-poi-id',
    name: '您的POI名称',
    position: [x, y, z],  // 3D位置
    color: '#ff6b6b',     // POI标记颜色
    description: '描述文字',
    details: '详细信息',
    model: {
      // GLB格式示例
      url: '/models/glb/your-model.glb',
      scale: 1,
      rotation: [0, 0, 0],
      fallback: 'default'

      // 或者 OBJ格式示例
      url: '/models/obj/your-model.obj',
      mtlUrl: '/models/obj/your-model.mtl',  // OBJ材质文件
      scale: 1,
      rotation: [0, 0, 0],
      fallback: 'default'
    }
  }
]
```

### 步骤3：配置模型属性

#### 🎛️ 可配置参数
- **url**: 模型文件路径（相对于public文件夹）
- **scale**: 缩放比例（数字，1=原始大小）
- **rotation**: 旋转角度（弧度制，[x轴, y轴, z轴]）
- **fallback**: 模型加载失败时的替代几何体

#### 📐 fallback类型
- `'pit'` - 矿坑样式（圆柱形坑洞）
- `'tower'` - 塔楼样式（观景台）
- `'path'` - 路径样式（步道）
- `'default'` - 默认立方体

## 🎮 使用方法

### 在应用中测试
1. 启动开发服务器：`npm run dev`
2. 访问：http://localhost:5173/
3. 点击"🎮 启用3D场景"
4. 点击"📦 启用3D模型"切换到3D模型模式

### 🔄 模式切换
- **几何体模式**：使用简单几何体，加载快速
- **3D模型模式**：使用真实3D模型，视觉效果更佳

## 🛠️ 模型优化建议

### 📏 文件大小
- 保持GLB文件小于5MB
- 使用纹理压缩
- 减少多边形数量

### 🎨 材质优化
- 使用PBR材质
- 纹理分辨率适中（512x512 或 1024x1024）
- 避免过多材质

### 🔧 几何体优化
- 面数控制在10K以下
- 使用LOD（细节层次）
- 移除不必要的细节

## 🚀 高级功能

### 动画支持
GLB模型可以包含动画，会自动播放：

```javascript
// 在Model3D组件中可以添加动画控制
const { scene, animations } = useGLTF(url)
```

### 交互增强
可以为模型添加更多交互功能：

```javascript
// 在POIComponent中添加悬停效果
onPointerOver={(e) => {
  // 模型悬停效果
}}
```

## 🔍 故障排除

### 模型不显示
1. 检查文件路径是否正确
2. 确认GLB文件格式正确
3. 查看浏览器控制台错误信息

### 性能问题
1. 减少模型复杂度
2. 使用纹理压缩
3. 启用几何体模式作为备选

### 位置不正确
1. 调整position参数
2. 修改scale缩放
3. 调整rotation旋转

## 📚 推荐工具

### 3D建模软件
- **Blender** (免费) - 完整的3D建模套件
- **SketchUp** - 建筑模型制作
- **3ds Max** - 专业3D建模

### 格式转换
- **Blender GLTF导出器** - 最佳GLB导出
- **Online GLTF Converter** - 在线转换工具
- **gltf-pipeline** - 命令行优化工具

## 🎯 示例模型

如果您没有现成的3D模型，可以：

1. **下载免费模型**：
   - Sketchfab (免费模型)
   - Google Poly Archive
   - Free3D

2. **使用程序化生成**：
   - 在代码中创建简单几何体
   - 使用Three.js内置几何体

3. **委托制作**：
   - 联系3D建模师
   - 使用AI生成工具

## 🎉 完成！

现在您已经可以在汤山矿坑公园AR平台中使用自己的3D模型了！

记住：
- 🎨 优先考虑用户体验
- ⚡ 保持良好的性能
- 🔄 提供备用方案
- 📱 确保移动端兼容性
