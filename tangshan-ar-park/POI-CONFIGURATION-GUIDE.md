# 🎯 POI_DATA配置完整指南

## 📍 配置文件位置

在 `src/App.jsx` 文件中找到 `POI_DATA` 数组（约第110-160行）

## 🔧 基础配置结构

```javascript
const POI_DATA = [
  {
    id: 'unique-poi-id',           // 唯一标识符
    name: 'POI显示名称',            // 用户看到的名称
    position: [x, y, z],          // 3D空间位置坐标
    color: '#ff6b6b',             // POI标记球体颜色
    description: '简短描述文字',    // 信息面板中的描述
    details: '详细信息文字',       // 信息面板中的详细信息
    model: {
      // 3D模型配置（见下方详细说明）
    }
  }
]
```

## 🎨 OBJ格式模型配置

### 基础OBJ配置
```javascript
model: {
  url: '/models/obj/your-model.obj',      // OBJ文件路径
  mtlUrl: '/models/obj/your-model.mtl',   // MTL材质文件路径（必需）
  scale: 1,                               // 缩放比例
  rotation: [0, 0, 0],                   // 旋转角度 [x轴, y轴, z轴]
  fallback: 'pit'                        // 备用几何体类型
}
```

### 高级OBJ配置
```javascript
model: {
  url: '/models/obj/complex-model.obj',
  mtlUrl: '/models/obj/complex-model.mtl',
  scale: 2.5,                    // 放大2.5倍
  rotation: [0, Math.PI/2, 0],   // Y轴旋转90度
  fallback: 'tower'
}
```

## 🎯 GLB格式模型配置

### 基础GLB配置
```javascript
model: {
  url: '/models/glb/your-model.glb',     // GLB文件路径
  scale: 1,                              // 缩放比例
  rotation: [0, 0, 0],                  // 旋转角度
  fallback: 'default'                    // 备用几何体类型
  // 注意：GLB格式不需要mtlUrl参数
}
```

## 📁 文件路径规则

### 路径格式
- **绝对路径**：以 `/` 开头，相对于 `public` 文件夹
- **OBJ文件**：`/models/obj/filename.obj`
- **MTL文件**：`/models/obj/filename.mtl`
- **GLB文件**：`/models/glb/filename.glb`

### 文件结构示例
```
public/
└── models/
    ├── obj/
    │   ├── main-pit.obj
    │   ├── main-pit.mtl
    │   ├── tower.obj
    │   └── tower.mtl
    ├── glb/
    │   ├── building.glb
    │   └── landscape.glb
    └── textures/
        ├── stone.jpg
        └── water.jpg
```

## 🎛️ 参数详解

### position: [x, y, z]
- **x轴**：左右位置（负数=左，正数=右）
- **y轴**：上下位置（负数=下，正数=上）
- **z轴**：前后位置（负数=后，正数=前）

```javascript
position: [0, 0, 0],     // 场景中心
position: [5, 2, -3],    // 右侧、上方、后方
position: [-8, -1, 4],   // 左侧、下方、前方
```

### scale: 缩放比例
```javascript
scale: 1,      // 原始大小
scale: 0.5,    // 缩小一半
scale: 2,      // 放大一倍
scale: [2, 1, 2],  // 不同轴向缩放：[x轴, y轴, z轴]
```

### rotation: [x, y, z] 旋转角度（弧度制）
```javascript
rotation: [0, 0, 0],              // 无旋转
rotation: [0, Math.PI/2, 0],      // Y轴旋转90度
rotation: [0, Math.PI, 0],        // Y轴旋转180度
rotation: [Math.PI/4, 0, 0],      // X轴旋转45度
```

### fallback: 备用几何体类型
当3D模型加载失败时显示的替代几何体：
- `'pit'` - 矿坑样式（圆柱形坑洞）
- `'tower'` - 塔楼样式（观景台）
- `'path'` - 路径样式（步道）
- `'default'` - 默认立方体

## 📝 完整配置示例

### 示例1：矿坑OBJ模型
```javascript
{
  id: 'main-pit',
  name: '主矿坑',
  position: [0, 0, 0],
  color: '#ff6b6b',
  description: '汤山矿坑公园的核心景观，深达数十米的废弃采石坑。',
  details: '深度约40米，面积约15公顷，开采时间1950-2010年',
  model: {
    url: '/models/obj/main-pit.obj',
    mtlUrl: '/models/obj/main-pit.mtl',
    scale: 1,
    rotation: [0, 0, 0],
    fallback: 'pit'
  }
}
```

### 示例2：观景台GLB模型
```javascript
{
  id: 'observation-deck',
  name: '观景台',
  position: [8, 2, -5],
  color: '#4ecdc4',
  description: '最佳观赏矿坑全景的位置。',
  details: '高度海拔120米，360度全景视野',
  model: {
    url: '/models/glb/observation-deck.glb',
    scale: 0.5,
    rotation: [0, Math.PI / 4, 0],
    fallback: 'tower'
  }
}
```

### 示例3：复杂建筑OBJ模型
```javascript
{
  id: 'visitor-center',
  name: '游客中心',
  position: [10, 0, 8],
  color: '#28a745',
  description: '现代化的游客服务中心。',
  details: '建筑面积2000平方米，包含展览厅、休息区、商店',
  model: {
    url: '/models/obj/visitor-center.obj',
    mtlUrl: '/models/obj/visitor-center.mtl',
    scale: 1.2,
    rotation: [0, -Math.PI / 3, 0],  // Y轴旋转-60度
    fallback: 'default'
  }
}
```

## 🔧 添加新POI的步骤

### 步骤1：准备3D模型文件
1. **OBJ格式**：将 `.obj` 和 `.mtl` 文件放入 `public/models/obj/`
2. **GLB格式**：将 `.glb` 文件放入 `public/models/glb/`

### 步骤2：编辑配置文件
1. 打开 `src/App.jsx`
2. 找到 `POI_DATA` 数组
3. 在数组末尾添加新的POI配置

### 步骤3：配置参数
```javascript
// 在POI_DATA数组中添加
{
  id: 'new-poi',                    // 修改为唯一ID
  name: '新POI名称',                 // 修改为实际名称
  position: [x, y, z],             // 设置3D位置
  color: '#颜色代码',                // 设置标记颜色
  description: '描述文字',          // 添加描述
  details: '详细信息',              // 添加详细信息
  model: {
    url: '/models/obj/your-model.obj',      // 设置模型路径
    mtlUrl: '/models/obj/your-model.mtl',   // 设置材质路径
    scale: 1,                               // 调整大小
    rotation: [0, 0, 0],                   // 调整旋转
    fallback: 'default'                     // 设置备用几何体
  }
}
```

### 步骤4：测试和调整
1. 保存文件
2. 刷新浏览器
3. 切换到3D场景
4. 启用3D模型模式
5. 检查新POI是否正确显示
6. 根据需要调整 `position`、`scale`、`rotation` 参数

## ⚠️ 常见注意事项

### 文件路径
- ✅ 正确：`/models/obj/model.obj`
- ❌ 错误：`models/obj/model.obj`（缺少开头的 `/`）
- ❌ 错误：`./models/obj/model.obj`（不要使用相对路径）

### OBJ和MTL文件
- ✅ OBJ文件必须配对MTL文件
- ✅ 文件名要完全匹配（除了扩展名）
- ❌ 不要只提供OBJ文件而忽略MTL文件

### 参数类型
- ✅ position: `[0, 1, 2]` （数组）
- ✅ scale: `1` 或 `[1, 2, 1]` （数字或数组）
- ✅ rotation: `[0, Math.PI, 0]` （弧度制数组）
- ❌ 不要使用角度制，要使用弧度制

现在您可以轻松配置任何OBJ或GLB格式的3D模型了！🎨
